{"version": 3, "sources": ["../../../../property-information/lib/util/schema.js", "../../../../property-information/lib/util/merge.js", "../../../../property-information/lib/normalize.js", "../../../../property-information/lib/util/info.js", "../../../../property-information/lib/util/types.js", "../../../../property-information/lib/util/defined-info.js", "../../../../property-information/lib/util/create.js", "../../../../property-information/lib/aria.js", "../../../../property-information/lib/util/case-sensitive-transform.js", "../../../../property-information/lib/util/case-insensitive-transform.js", "../../../../property-information/lib/html.js", "../../../../property-information/lib/svg.js", "../../../../property-information/lib/xlink.js", "../../../../property-information/lib/xmlns.js", "../../../../property-information/lib/xml.js", "../../../../property-information/lib/find.js", "../../../../property-information/index.js", "../../../../comma-separated-tokens/index.js", "../../../../hast-util-parse-selector/lib/index.js", "../../../../space-separated-tokens/index.js", "../../../../hastscript/lib/create-h.js", "../../../../hastscript/lib/svg-case-sensitive-tag-names.js", "../../../../hastscript/lib/index.js", "../../../../vfile-location/lib/index.js", "../../../../web-namespaces/index.js", "../../../../hast-util-from-parse5/lib/index.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/schema.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/merge.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/normalize.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/info.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/types.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/defined-info.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/create.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/xlink.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/xml.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/case-sensitive-transform.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/util/case-insensitive-transform.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/xmlns.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/aria.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/html.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/svg.js", "../../../../hast-util-to-parse5/node_modules/property-information/lib/find.js", "../../../../hast-util-to-parse5/node_modules/property-information/index.js", "../../../../hast-util-to-parse5/lib/index.js", "../../../../html-void-elements/index.js", "../../../../hast-util-raw/lib/index.js", "../../../../rehype-raw/lib/index.js"], "sourcesContent": ["/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nexport class Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal\n    this.property = property\n\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\nSchema.prototype.normal = {}\nSchema.prototype.property = {}\nSchema.prototype.space = undefined\n", "/**\n * @import {Info, Space} from 'property-information'\n */\n\nimport {Schema} from './schema.js'\n\n/**\n * @param {ReadonlyArray<Schema>} definitions\n *   Definitions.\n * @param {Space | undefined} [space]\n *   Space.\n * @returns {Schema}\n *   Schema.\n */\nexport function merge(definitions, space) {\n  /** @type {Record<string, Info>} */\n  const property = {}\n  /** @type {Record<string, string>} */\n  const normal = {}\n\n  for (const definition of definitions) {\n    Object.assign(property, definition.property)\n    Object.assign(normal, definition.normal)\n  }\n\n  return new Schema(property, normal, space)\n}\n", "/**\n * Get the cleaned case insensitive form of an attribute or property.\n *\n * @param {string} value\n *   An attribute-like or property-like name.\n * @returns {string}\n *   Value that can be used to look up the properly cased property on a\n *   `Schema`.\n */\nexport function normalize(value) {\n  return value.toLowerCase()\n}\n", "/**\n * @import {Info as InfoType} from 'property-information'\n */\n\n/** @type {InfoType} */\nexport class Info {\n  /**\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute) {\n    this.attribute = attribute\n    this.property = property\n  }\n}\n\nInfo.prototype.attribute = ''\nInfo.prototype.booleanish = false\nInfo.prototype.boolean = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.defined = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.number = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.property = ''\nInfo.prototype.spaceSeparated = false\nInfo.prototype.space = undefined\n", "let powers = 0\n\nexport const boolean = increment()\nexport const booleanish = increment()\nexport const overloadedBoolean = increment()\nexport const number = increment()\nexport const spaceSeparated = increment()\nexport const commaSeparated = increment()\nexport const commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n", "/**\n * @import {Space} from 'property-information'\n */\n\nimport {Info} from './info.js'\nimport * as types from './types.js'\n\nconst checks = /** @type {ReadonlyArray<keyof typeof types>} */ (\n  Object.keys(types)\n)\n\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @param {number | null | undefined} [mask]\n   *   Mask.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & types[check]) === types[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @template {keyof DefinedInfo} Key\n *   Key type.\n * @param {DefinedInfo} values\n *   Info.\n * @param {Key} key\n *   Key.\n * @param {DefinedInfo[Key]} value\n *   Value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n", "/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\nimport {normalize} from '../normalize.js'\nimport {DefinedInfo} from './defined-info.js'\nimport {Schema} from './schema.js'\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nexport function create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {}\n  /** @type {Record<string, string>} */\n  const normals = {}\n\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new DefinedInfo(\n      property,\n      definition.transform(definition.attributes || {}, property),\n      value,\n      definition.space\n    )\n\n    if (\n      definition.mustUseProperty &&\n      definition.mustUseProperty.includes(property)\n    ) {\n      info.mustUseProperty = true\n    }\n\n    properties[property] = info\n\n    normals[normalize(property)] = property\n    normals[normalize(info.attribute)] = property\n  }\n\n  return new Schema(properties, normals, definition.space)\n}\n", "import {create} from './util/create.js'\nimport {booleanish, number, spaceSeparated} from './util/types.js'\n\nexport const aria = create({\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  },\n  transform(_, property) {\n    return property === 'role'\n      ? property\n      : 'aria-' + property.slice(4).toLowerCase()\n  }\n})\n", "/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} attribute\n *   Attribute.\n * @returns {string}\n *   Transformed attribute.\n */\nexport function caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n", "import {caseSensitiveTransform} from './case-sensitive-transform.js'\n\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Transformed property.\n */\nexport function caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n", "import {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\nimport {create} from './util/create.js'\nimport {\n  booleanish,\n  boolean,\n  commaSeparated,\n  number,\n  overloadedBoolean,\n  spaceSeparated\n} from './util/types.js'\n\nexport const html = create({\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    blocking: spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: overloadedBoolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shadowRootClonable: boolean,\n    shadowRootDelegatesFocus: boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  },\n  space: 'html',\n  transform: caseInsensitiveTransform\n})\n", "import {caseSensitiveTransform} from './util/case-sensitive-transform.js'\nimport {create} from './util/create.js'\nimport {\n  boolean,\n  commaOrSpaceSeparated,\n  commaSeparated,\n  number,\n  spaceSeparated\n} from './util/types.js'\n\nexport const svg = create({\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  properties: {\n    about: commaOrSpaceSeparated,\n    accentHeight: number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: number,\n    amplitude: number,\n    arabicForm: null,\n    ascent: number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: number,\n    by: null,\n    calcMode: null,\n    capHeight: number,\n    className: spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: number,\n    diffuseConstant: number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: number,\n    dominantBaseline: null,\n    download: boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: commaSeparated,\n    g2: commaSeparated,\n    glyphName: commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: number,\n    horizOriginX: number,\n    horizOriginY: number,\n    id: null,\n    ideographic: number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: number,\n    k: number,\n    k1: number,\n    k2: number,\n    k3: number,\n    k4: number,\n    kernelMatrix: commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: number,\n    overlineThickness: number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: number,\n    pointsAtY: number,\n    pointsAtZ: number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: commaOrSpaceSeparated,\n    rev: commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: commaOrSpaceSeparated,\n    requiredFeatures: commaOrSpaceSeparated,\n    requiredFonts: commaOrSpaceSeparated,\n    requiredFormats: commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: number,\n    specularExponent: number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: number,\n    strikethroughThickness: number,\n    string: null,\n    stroke: null,\n    strokeDashArray: commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: number,\n    strokeOpacity: number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: commaOrSpaceSeparated,\n    tabIndex: number,\n    tableValues: null,\n    target: null,\n    targetX: number,\n    targetY: number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: number,\n    underlineThickness: number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: number,\n    values: null,\n    vAlphabetic: number,\n    vMathematical: number,\n    vectorEffect: null,\n    vHanging: number,\n    vIdeographic: number,\n    version: null,\n    vertAdvY: number,\n    vertOriginX: number,\n    vertOriginY: number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  },\n  space: 'svg',\n  transform: caseSensitiveTransform\n})\n", "import {create} from './util/create.js'\n\nexport const xlink = create({\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  },\n  space: 'xlink',\n  transform(_, property) {\n    return 'xlink:' + property.slice(5).toLowerCase()\n  }\n})\n", "import {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const xmlns = create({\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  properties: {xmlnsXLink: null, xmlns: null},\n  space: 'xmlns',\n  transform: caseInsensitiveTransform\n})\n", "import {create} from './util/create.js'\n\nexport const xml = create({\n  properties: {xmlBase: null, xmlLang: null, xmlSpace: null},\n  space: 'xml',\n  transform(_, property) {\n    return 'xml:' + property.slice(3).toLowerCase()\n  }\n})\n", "/**\n * @import {Schema} from 'property-information'\n */\n\nimport {DefinedInfo} from './util/defined-info.js'\nimport {Info} from './util/info.js'\nimport {normalize} from './normalize.js'\n\nconst cap = /[A-Z]/g\nconst dash = /-[a-z]/g\nconst valid = /^data[-\\w.:]+$/i\n\n/**\n * Look up info on a property.\n *\n * In most cases the given `schema` contains info on the property.\n * All standard,\n * most legacy,\n * and some non-standard properties are supported.\n * For these cases,\n * the returned `Info` has hints about the value of the property.\n *\n * `name` can also be a valid data attribute or property,\n * in which case an `Info` object with the correctly cased `attribute` and\n * `property` is returned.\n *\n * `name` can be an unknown attribute,\n * in which case an `Info` object with `attribute` and `property` set to the\n * given name is returned.\n * It is not recommended to provide unsupported legacy or recently specced\n * properties.\n *\n *\n * @param {Schema} schema\n *   Schema;\n *   either the `html` or `svg` export.\n * @param {string} value\n *   An attribute-like or property-like name;\n *   it will be passed through `normalize` to hopefully find the correct info.\n * @returns {Info}\n *   Info.\n */\nexport function find(schema, value) {\n  const normal = normalize(value)\n  let property = value\n  let Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      property = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(property, value)\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Kebab.\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Camel.\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n", "// Note: types exposed from `index.d.ts`.\nimport {merge} from './lib/util/merge.js'\nimport {aria} from './lib/aria.js'\nimport {html as htmlBase} from './lib/html.js'\nimport {svg as svgBase} from './lib/svg.js'\nimport {xlink} from './lib/xlink.js'\nimport {xmlns} from './lib/xmlns.js'\nimport {xml} from './lib/xml.js'\n\nexport {hastToReact} from './lib/hast-to-react.js'\n\nexport const html = merge([aria, htmlBase, xlink, xmlns, xml], 'html')\n\nexport {find} from './lib/find.js'\nexport {normalize} from './lib/normalize.js'\n\nexport const svg = merge([aria, svgBase, xlink, xmlns, xml], 'svg')\n", "/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n/**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */\n\n/**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  /** @type {Array<string>} */\n  const tokens = []\n  const input = String(value || '')\n  let index = input.indexOf(',')\n  let start = 0\n  /** @type {boolean} */\n  let end = false\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    const token = input.slice(start, index).trim()\n\n    if (token || !end) {\n      tokens.push(token)\n    }\n\n    start = index + 1\n    index = input.indexOf(',', start)\n  }\n\n  return tokens\n}\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nexport function stringify(values, options) {\n  const settings = options || {}\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values\n\n  return input\n    .join(\n      (settings.padRight ? ' ' : '') +\n        ',' +\n        (settings.padLeft === false ? '' : ' ')\n    )\n    .trim()\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n */\n\n/**\n * @template {string} SimpleSelector\n *   Selector type.\n * @template {string} DefaultTagName\n *   Default tag name.\n * @typedef {(\n *   SimpleSelector extends ''\n *     ? DefaultTagName\n *     : SimpleSelector extends `${infer TagName}.${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends `${infer TagName}#${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends string\n *     ? SimpleSelector\n *     : DefaultTagName\n * )} ExtractTagName\n *   Extract tag name from a simple selector.\n */\n\nconst search = /[#.]/g\n\n/**\n * Create a hast element from a simple CSS selector.\n *\n * @template {string} Selector\n *   Type of selector.\n * @template {string} [DefaultTagName='div']\n *   Type of default tag name (default: `'div'`).\n * @param {Selector | null | undefined} [selector]\n *   Simple CSS selector (optional).\n *\n *   Can contain a tag name (`foo`), classes (`.bar`), and an ID (`#baz`).\n *   Multiple classes are allowed.\n *   Uses the last ID if multiple IDs are found.\n * @param {DefaultTagName | null | undefined} [defaultTagName='div']\n *   Tag name to use if `selector` does not specify one (default: `'div'`).\n * @returns {Element & {tagName: ExtractTagName<Selector, DefaultTagName>}}\n *   Built element.\n */\nexport function parseSelector(selector, defaultTagName) {\n  const value = selector || ''\n  /** @type {Properties} */\n  const props = {}\n  let start = 0\n  /** @type {string | undefined} */\n  let previous\n  /** @type {string | undefined} */\n  let tagName\n\n  while (start < value.length) {\n    search.lastIndex = start\n    const match = search.exec(value)\n    const subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        tagName = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (Array.isArray(props.className)) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {\n    type: 'element',\n    // @ts-expect-error: tag name is parsed.\n    tagName: tagName || defaultTagName || 'div',\n    properties: props,\n    children: []\n  }\n}\n", "/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  const input = String(value || '').trim()\n  return input ? input.split(/[ \\t\\n\\r\\f]+/g) : []\n}\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nexport function stringify(values) {\n  return values.join(' ').trim()\n}\n", "/**\n * @import {Element, Nodes, RootContent, Root} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n/**\n * @typedef {Array<Nodes | PrimitiveChild>} ArrayChildNested\n *   List of children (deep).\n */\n\n/**\n * @typedef {Array<ArrayChildNested | Nodes | PrimitiveChild>} ArrayChild\n *   List of children.\n */\n\n/**\n * @typedef {Array<number | string>} ArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n */\n\n/**\n * @typedef {ArrayChild | Nodes | PrimitiveChild} Child\n *   Acceptable child value.\n */\n\n/**\n * @typedef {number | string | null | undefined} PrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n */\n\n/**\n * @typedef {boolean | number | string | null | undefined} PrimitiveValue\n *   Primitive property value.\n */\n\n/**\n * @typedef {Record<string, PropertyValue | Style>} Properties\n *   Acceptable value for element properties.\n */\n\n/**\n * @typedef {ArrayValue | PrimitiveValue} PropertyValue\n *   Primitive value or list value.\n */\n\n/**\n * @typedef {Element | Root} Result\n *   Result from a `h` (or `s`) call.\n */\n\n/**\n * @typedef {number | string} StyleValue\n *   Value for a CSS style field.\n */\n\n/**\n * @typedef {Record<string, StyleValue>} Style\n *   Supported value of a `style` prop.\n */\n\nimport {parse as parseCommas} from 'comma-separated-tokens'\nimport {parseSelector} from 'hast-util-parse-selector'\nimport {find, normalize} from 'property-information'\nimport {parse as parseSpaces} from 'space-separated-tokens'\n\n/**\n * @param {Schema} schema\n *   Schema to use.\n * @param {string} defaultTagName\n *   Default tag name.\n * @param {ReadonlyArray<string> | undefined} [caseSensitive]\n *   Case-sensitive tag names (default: `undefined`).\n * @returns\n *   `h`.\n */\nexport function createH(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : undefined\n\n  /**\n   * Hyperscript compatible DSL for creating virtual hast trees.\n   *\n   * @overload\n   * @param {null | undefined} [selector]\n   * @param {...Child} children\n   * @returns {Root}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {Properties} properties\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @param {string | null | undefined} [selector]\n   *   Selector.\n   * @param {Child | Properties | null | undefined} [properties]\n   *   Properties (or first child) (default: `undefined`).\n   * @param {...Child} children\n   *   Children.\n   * @returns {Result}\n   *   Result.\n   */\n  function h(selector, properties, ...children) {\n    /** @type {Result} */\n    let node\n\n    if (selector === null || selector === undefined) {\n      node = {type: 'root', children: []}\n      // Properties are not supported for roots.\n      const child = /** @type {Child} */ (properties)\n      children.unshift(child)\n    } else {\n      node = parseSelector(selector, defaultTagName)\n      // Normalize the name.\n      const lower = node.tagName.toLowerCase()\n      const adjusted = adjust ? adjust.get(lower) : undefined\n      node.tagName = adjusted || lower\n\n      // Handle properties.\n      if (isChild(properties)) {\n        children.unshift(properties)\n      } else {\n        for (const [key, value] of Object.entries(properties)) {\n          addProperty(schema, node.properties, key, value)\n        }\n      }\n    }\n\n    // Handle children.\n    for (const child of children) {\n      addChild(node.children, child)\n    }\n\n    if (node.type === 'element' && node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  return h\n}\n\n/**\n * Check if something is properties or a child.\n *\n * @param {Child | Properties} value\n *   Value to check.\n * @returns {value is Child}\n *   Whether `value` is definitely a child.\n */\nfunction isChild(value) {\n  // Never properties if not an object.\n  if (value === null || typeof value !== 'object' || Array.isArray(value)) {\n    return true\n  }\n\n  // Never node without `type`; that’s the main discriminator.\n  if (typeof value.type !== 'string') return false\n\n  // Slower check: never property value if object or array with\n  // non-number/strings.\n  const record = /** @type {Record<string, unknown>} */ (value)\n  const keys = Object.keys(value)\n\n  for (const key of keys) {\n    const value = record[key]\n\n    if (value && typeof value === 'object') {\n      if (!Array.isArray(value)) return true\n\n      const list = /** @type {ReadonlyArray<unknown>} */ (value)\n\n      for (const item of list) {\n        if (typeof item !== 'number' && typeof item !== 'string') {\n          return true\n        }\n      }\n    }\n  }\n\n  // Also see empty `children` as a node.\n  if ('children' in value && Array.isArray(value.children)) {\n    return true\n  }\n\n  // Default to properties, someone can always pass an empty object,\n  // put `data: {}` in a node,\n  // or wrap it in an array.\n  return false\n}\n\n/**\n * @param {Schema} schema\n *   Schema.\n * @param {Properties} properties\n *   Properties object.\n * @param {string} key\n *   Property name.\n * @param {PropertyValue | Style} value\n *   Property value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = find(schema, key)\n  /** @type {PropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === null || value === undefined) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = parseSpaces(value)\n    } else if (info.commaSeparated) {\n      result = parseCommas(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = parseSpaces(parseCommas(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = [...value]\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<number | string>} */\n    const finalResult = []\n\n    for (const item of result) {\n      // Assume no booleans in array.\n      finalResult.push(\n        /** @type {number | string} */ (\n          parsePrimitive(info, info.property, item)\n        )\n      )\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // Assume no booleans in `className`.\n    result = properties.className.concat(\n      /** @type {Array<number | string> | number | string} */ (result)\n    )\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<RootContent>} nodes\n *   Children.\n * @param {Child} value\n *   Child.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChild(nodes, value) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'number' || typeof value === 'string') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    for (const child of value) {\n      addChild(nodes, child)\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n *   Property information.\n * @param {string} name\n *   Property name.\n * @param {PrimitiveValue} value\n *   Property value.\n * @returns {PrimitiveValue}\n *   Property value.\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || normalize(value) === normalize(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {Style} styles\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(styles) {\n  /** @type {Array<string>} */\n  const result = []\n\n  for (const [key, value] of Object.entries(styles)) {\n    result.push([key, value].join(': '))\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {ReadonlyArray<string>} values\n *   List of properly cased keys.\n * @returns {Map<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Map<string, string>} */\n  const result = new Map()\n\n  for (const value of values) {\n    result.set(value.toLowerCase(), value)\n  }\n\n  return result\n}\n", "/**\n * List of case-sensitive SVG tag names.\n *\n * @type {ReadonlyArray<string>}\n */\nexport const svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n", "// Register the JSX namespace on `h`.\n/**\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n */\n\n// Register the JSX namespace on `s`.\n/**\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n */\n\nimport {html, svg} from 'property-information'\nimport {createH} from './create-h.js'\nimport {svgCaseSensitiveTagNames} from './svg-case-sensitive-tag-names.js'\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nexport const h = createH(html, 'div')\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nexport const s = createH(svg, 'g', svgCaseSensitiveTagNames)\n", "/**\n * @import {VFile, Value} from 'vfile'\n * @import {Location} from 'vfile-location'\n */\n\n/**\n * Create an index of the given document to translate between line/column and\n * offset based positional info.\n *\n * Also implemented in Rust in [`wooorm/markdown-rs`][markdown-rs].\n *\n * [markdown-rs]: https://github.com/wooorm/markdown-rs/blob/main/src/util/location.rs\n *\n * @param {VFile | Value} file\n *   File to index.\n * @returns {Location}\n *   Accessors for index.\n */\nexport function location(file) {\n  const value = String(file)\n  /**\n   * List, where each index is a line number (0-based), and each value is the\n   * byte index *after* where the line ends.\n   *\n   * @type {Array<number>}\n   */\n  const indices = []\n\n  return {toOffset, toPoint}\n\n  /** @type {Location['toPoint']} */\n  function toPoint(offset) {\n    if (typeof offset === 'number' && offset > -1 && offset <= value.length) {\n      let index = 0\n\n      while (true) {\n        let end = indices[index]\n\n        if (end === undefined) {\n          const eol = next(value, indices[index - 1])\n          end = eol === -1 ? value.length + 1 : eol + 1\n          indices[index] = end\n        }\n\n        if (end > offset) {\n          return {\n            line: index + 1,\n            column: offset - (index > 0 ? indices[index - 1] : 0) + 1,\n            offset\n          }\n        }\n\n        index++\n      }\n    }\n  }\n\n  /** @type {Location['toOffset']} */\n  function toOffset(point) {\n    if (\n      point &&\n      typeof point.line === 'number' &&\n      typeof point.column === 'number' &&\n      !Number.isNaN(point.line) &&\n      !Number.isNaN(point.column)\n    ) {\n      while (indices.length < point.line) {\n        const from = indices[indices.length - 1]\n        const eol = next(value, from)\n        const end = eol === -1 ? value.length + 1 : eol + 1\n        if (from === end) break\n        indices.push(end)\n      }\n\n      const offset =\n        (point.line > 1 ? indices[point.line - 2] : 0) + point.column - 1\n      // The given `column` could not exist on this line.\n      if (offset < indices[point.line - 1]) return offset\n    }\n  }\n}\n\n/**\n * @param {string} value\n * @param {number} from\n */\nfunction next(value, from) {\n  const cr = value.indexOf('\\r', from)\n  const lf = value.indexOf('\\n', from)\n  if (lf === -1) return cr\n  if (cr === -1 || cr + 1 === lf) return lf\n  return cr < lf ? cr : lf\n}\n", "/**\n * Map of web namespaces.\n *\n * @type {Record<string, string>}\n */\nexport const webNamespaces = {\n  html: 'http://www.w3.org/1999/xhtml',\n  mathml: 'http://www.w3.org/1998/Math/MathML',\n  svg: 'http://www.w3.org/2000/svg',\n  xlink: 'http://www.w3.org/1999/xlink',\n  xml: 'http://www.w3.org/XML/1998/namespace',\n  xmlns: 'http://www.w3.org/2000/xmlns/'\n}\n", "/**\n * @import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, No<PERSON>, <PERSON>Content, Root} from 'hast'\n * @import {DefaultTreeAdapterMap, Token} from 'parse5'\n * @import {Schema} from 'property-information'\n * @import {Point, Position} from 'unist'\n * @import {VFile} from 'vfile'\n * @import {Options} from 'hast-util-from-parse5'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {VFile | undefined} file\n *   Corresponding file.\n * @property {boolean} location\n *   Whether location info was found.\n * @property {Schema} schema\n *   Current schema.\n * @property {boolean | undefined} verbose\n *   Add extra positional info.\n */\n\nimport {ok as assert} from 'devlop'\nimport {h, s} from 'hastscript'\nimport {find, html, svg} from 'property-information'\nimport {location} from 'vfile-location'\nimport {webNamespaces} from 'web-namespaces'\n\nconst own = {}.hasOwnProperty\n/** @type {unknown} */\n// type-coverage:ignore-next-line\nconst proto = Object.prototype\n\n/**\n * Transform a `parse5` AST to hast.\n *\n * @param {DefaultTreeAdapterMap['node']} tree\n *   `parse5` tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   hast tree.\n */\nexport function fromParse5(tree, options) {\n  const settings = options || {}\n\n  return one(\n    {\n      file: settings.file || undefined,\n      location: false,\n      schema: settings.space === 'svg' ? svg : html,\n      verbose: settings.verbose || false\n    },\n    tree\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} node\n *   p5 node.\n * @returns {Nodes}\n *   hast node.\n */\nfunction one(state, node) {\n  /** @type {Nodes} */\n  let result\n\n  switch (node.nodeName) {\n    case '#comment': {\n      const reference = /** @type {DefaultTreeAdapterMap['commentNode']} */ (\n        node\n      )\n      result = {type: 'comment', value: reference.data}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#document':\n    case '#document-fragment': {\n      const reference =\n        /** @type {DefaultTreeAdapterMap['document'] | DefaultTreeAdapterMap['documentFragment']} */ (\n          node\n        )\n      const quirksMode =\n        'mode' in reference\n          ? reference.mode === 'quirks' || reference.mode === 'limited-quirks'\n          : false\n\n      result = {\n        type: 'root',\n        children: all(state, node.childNodes),\n        data: {quirksMode}\n      }\n\n      if (state.file && state.location) {\n        const document = String(state.file)\n        const loc = location(document)\n        const start = loc.toPoint(0)\n        const end = loc.toPoint(document.length)\n        // Always defined as we give valid input.\n        assert(start, 'expected `start`')\n        assert(end, 'expected `end`')\n        result.position = {start, end}\n      }\n\n      return result\n    }\n\n    case '#documentType': {\n      const reference = /** @type {DefaultTreeAdapterMap['documentType']} */ (\n        node\n      )\n      result = {type: 'doctype'}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#text': {\n      const reference = /** @type {DefaultTreeAdapterMap['textNode']} */ (node)\n      result = {type: 'text', value: reference.value}\n      patch(state, reference, result)\n      return result\n    }\n\n    // Element.\n    default: {\n      const reference = /** @type {DefaultTreeAdapterMap['element']} */ (node)\n      result = element(state, reference)\n      return result\n    }\n  }\n}\n\n/**\n * Transform children.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Array<DefaultTreeAdapterMap['node']>} nodes\n *   Nodes.\n * @returns {Array<RootContent>}\n *   hast nodes.\n */\nfunction all(state, nodes) {\n  let index = -1\n  /** @type {Array<RootContent>} */\n  const results = []\n\n  while (++index < nodes.length) {\n    // Assume no roots in `nodes`.\n    const result = /** @type {RootContent} */ (one(state, nodes[index]))\n    results.push(result)\n  }\n\n  return results\n}\n\n/**\n * Transform an element.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['element']} node\n *   `parse5` node to transform.\n * @returns {Element}\n *   hast node.\n */\nfunction element(state, node) {\n  const schema = state.schema\n\n  state.schema = node.namespaceURI === webNamespaces.svg ? svg : html\n\n  // Props.\n  let index = -1\n  /** @type {Record<string, string>} */\n  const properties = {}\n\n  while (++index < node.attrs.length) {\n    const attribute = node.attrs[index]\n    const name =\n      (attribute.prefix ? attribute.prefix + ':' : '') + attribute.name\n    if (!own.call(proto, name)) {\n      properties[name] = attribute.value\n    }\n  }\n\n  // Build.\n  const x = state.schema.space === 'svg' ? s : h\n  const result = x(node.tagName, properties, all(state, node.childNodes))\n  patch(state, node, result)\n\n  // Switch content.\n  if (result.tagName === 'template') {\n    const reference = /** @type {DefaultTreeAdapterMap['template']} */ (node)\n    const pos = reference.sourceCodeLocation\n    const startTag = pos && pos.startTag && position(pos.startTag)\n    const endTag = pos && pos.endTag && position(pos.endTag)\n\n    // Root in, root out.\n    const content = /** @type {Root} */ (one(state, reference.content))\n\n    if (startTag && endTag && state.file) {\n      content.position = {start: startTag.end, end: endTag.start}\n    }\n\n    result.content = content\n  }\n\n  state.schema = schema\n\n  return result\n}\n\n/**\n * Patch positional info from `from` onto `to`.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} from\n *   p5 node.\n * @param {Nodes} to\n *   hast node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(state, from, to) {\n  if ('sourceCodeLocation' in from && from.sourceCodeLocation && state.file) {\n    const position = createLocation(state, to, from.sourceCodeLocation)\n\n    if (position) {\n      state.location = true\n      to.position = position\n    }\n  }\n}\n\n/**\n * Create clean positional information.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node.\n * @param {Token.ElementLocation} location\n *   p5 location info.\n * @returns {Position | undefined}\n *   Position, or nothing.\n */\nfunction createLocation(state, node, location) {\n  const result = position(location)\n\n  if (node.type === 'element') {\n    const tail = node.children[node.children.length - 1]\n\n    // Bug for unclosed with children.\n    // See: <https://github.com/inikulin/parse5/issues/109>.\n    if (\n      result &&\n      !location.endTag &&\n      tail &&\n      tail.position &&\n      tail.position.end\n    ) {\n      result.end = Object.assign({}, tail.position.end)\n    }\n\n    if (state.verbose) {\n      /** @type {Record<string, Position | undefined>} */\n      const properties = {}\n      /** @type {string} */\n      let key\n\n      if (location.attrs) {\n        for (key in location.attrs) {\n          if (own.call(location.attrs, key)) {\n            properties[find(state.schema, key).property] = position(\n              location.attrs[key]\n            )\n          }\n        }\n      }\n\n      assert(location.startTag, 'a start tag should exist')\n      const opening = position(location.startTag)\n      const closing = location.endTag ? position(location.endTag) : undefined\n      /** @type {ElementData['position']} */\n      const data = {opening}\n      if (closing) data.closing = closing\n      data.properties = properties\n\n      node.data = {position: data}\n    }\n  }\n\n  return result\n}\n\n/**\n * Turn a p5 location into a position.\n *\n * @param {Token.Location} loc\n *   Location.\n * @returns {Position | undefined}\n *   Position or nothing.\n */\nfunction position(loc) {\n  const start = point({\n    line: loc.startLine,\n    column: loc.startCol,\n    offset: loc.startOffset\n  })\n  const end = point({\n    line: loc.endLine,\n    column: loc.endCol,\n    offset: loc.endOffset\n  })\n\n  // @ts-expect-error: we do use `undefined` for points if one or the other\n  // exists.\n  return start || end ? {start, end} : undefined\n}\n\n/**\n * Filter out invalid points.\n *\n * @param {Point} point\n *   Point with potentially `undefined` values.\n * @returns {Point | undefined}\n *   Point or nothing.\n */\nfunction point(point) {\n  return point.line && point.column ? point : undefined\n}\n", "/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nexport class Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property\n    this.normal = normal\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {}\n/** @type {Normal} */\nSchema.prototype.normal = {}\n/** @type {string|null} */\nSchema.prototype.space = null\n", "/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\nimport {Schema} from './schema.js'\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nexport function merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  let index = -1\n\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property)\n    Object.assign(normal, definitions[index].normal)\n  }\n\n  return new Schema(property, normal, space)\n}\n", "/**\n * @param {string} value\n * @returns {string}\n */\nexport function normalize(value) {\n  return value.toLowerCase()\n}\n", "export class Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property\n    /** @type {string} */\n    this.attribute = attribute\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null\nInfo.prototype.boolean = false\nInfo.prototype.booleanish = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.number = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.spaceSeparated = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.defined = false\n", "let powers = 0\n\nexport const boolean = increment()\nexport const booleanish = increment()\nexport const overloadedBoolean = increment()\nexport const number = increment()\nexport const spaceSeparated = increment()\nexport const commaSeparated = increment()\nexport const commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n", "import {Info} from './info.js'\nimport * as types from './types.js'\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(types)\n\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & types[check]) === types[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value\n  }\n}\n", "/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\nimport {normalize} from '../normalize.js'\nimport {Schema} from './schema.js'\nimport {DefinedInfo} from './defined-info.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nexport function create(definition) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  /** @type {string} */\n  let prop\n\n  for (prop in definition.properties) {\n    if (own.call(definition.properties, prop)) {\n      const value = definition.properties[prop]\n      const info = new DefinedInfo(\n        prop,\n        definition.transform(definition.attributes || {}, prop),\n        value,\n        definition.space\n      )\n\n      if (\n        definition.mustUseProperty &&\n        definition.mustUseProperty.includes(prop)\n      ) {\n        info.mustUseProperty = true\n      }\n\n      property[prop] = info\n\n      normal[normalize(prop)] = prop\n      normal[normalize(info.attribute)] = prop\n    }\n  }\n\n  return new Schema(property, normal, definition.space)\n}\n", "import {create} from './util/create.js'\n\nexport const xlink = create({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase()\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n", "import {create} from './util/create.js'\n\nexport const xml = create({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase()\n  },\n  properties: {xmlLang: null, xmlBase: null, xmlSpace: null}\n})\n", "/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nexport function caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n", "import {caseSensitiveTransform} from './case-sensitive-transform.js'\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nexport function caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n", "import {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const xmlns = create({\n  space: 'xmlns',\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  transform: caseInsensitiveTransform,\n  properties: {xmlns: null, xmlnsXLink: null}\n})\n", "import {booleanish, number, spaceSeparated} from './util/types.js'\nimport {create} from './util/create.js'\n\nexport const aria = create({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n", "import {\n  boolean,\n  overloadedBoolean,\n  booleanish,\n  number,\n  spaceSeparated,\n  commaSeparated\n} from './util/types.js'\nimport {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const html = create({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    blocking: spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: boolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shadowRootClonable: boolean,\n    shadowRootDelegatesFocus: boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  }\n})\n", "import {\n  boolean,\n  number,\n  spaceSeparated,\n  commaSeparated,\n  commaOrSpaceSeparated\n} from './util/types.js'\nimport {create} from './util/create.js'\nimport {caseSensitiveTransform} from './util/case-sensitive-transform.js'\n\nexport const svg = create({\n  space: 'svg',\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  transform: caseSensitiveTransform,\n  properties: {\n    about: commaOrSpaceSeparated,\n    accentHeight: number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: number,\n    amplitude: number,\n    arabicForm: null,\n    ascent: number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: number,\n    by: null,\n    calcMode: null,\n    capHeight: number,\n    className: spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: number,\n    diffuseConstant: number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: number,\n    dominantBaseline: null,\n    download: boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: commaSeparated,\n    g2: commaSeparated,\n    glyphName: commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: number,\n    horizOriginX: number,\n    horizOriginY: number,\n    id: null,\n    ideographic: number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: number,\n    k: number,\n    k1: number,\n    k2: number,\n    k3: number,\n    k4: number,\n    kernelMatrix: commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: number,\n    overlineThickness: number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: number,\n    pointsAtY: number,\n    pointsAtZ: number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: commaOrSpaceSeparated,\n    rev: commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: commaOrSpaceSeparated,\n    requiredFeatures: commaOrSpaceSeparated,\n    requiredFonts: commaOrSpaceSeparated,\n    requiredFormats: commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: number,\n    specularExponent: number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: number,\n    strikethroughThickness: number,\n    string: null,\n    stroke: null,\n    strokeDashArray: commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: number,\n    strokeOpacity: number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: commaOrSpaceSeparated,\n    tabIndex: number,\n    tableValues: null,\n    target: null,\n    targetX: number,\n    targetY: number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: number,\n    underlineThickness: number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: number,\n    values: null,\n    vAlphabetic: number,\n    vMathematical: number,\n    vectorEffect: null,\n    vHanging: number,\n    vIdeographic: number,\n    version: null,\n    vertAdvY: number,\n    vertOriginX: number,\n    vertOriginY: number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  }\n})\n", "/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\nimport {normalize} from './normalize.js'\nimport {DefinedInfo} from './util/defined-info.js'\nimport {Info} from './util/info.js'\n\nconst valid = /^data[-\\w.:]+$/i\nconst dash = /-[a-z]/g\nconst cap = /[A-Z]/g\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nexport function find(schema, value) {\n  const normal = normalize(value)\n  let prop = value\n  let Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n", "/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\nimport {merge} from './lib/util/merge.js'\nimport {xlink} from './lib/xlink.js'\nimport {xml} from './lib/xml.js'\nimport {xmlns} from './lib/xmlns.js'\nimport {aria} from './lib/aria.js'\nimport {html as htmlBase} from './lib/html.js'\nimport {svg as svgBase} from './lib/svg.js'\n\nexport {find} from './lib/find.js'\nexport {hastToReact} from './lib/hast-to-react.js'\nexport {normalize} from './lib/normalize.js'\nexport const html = merge([xml, xlink, xmlns, aria, htmlBase], 'html')\nexport const svg = merge([xml, xlink, xmlns, aria, svgBase], 'svg')\n", "/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Doctype} Doctype\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').RootContent} RootContent\n * @typedef {import('hast').Text} Text\n *\n * @typedef {import('parse5').DefaultTreeAdapterMap['document']} Parse5Document\n * @typedef {import('parse5').DefaultTreeAdapterMap['documentFragment']} Parse5Fragment\n * @typedef {import('parse5').DefaultTreeAdapterMap['element']} Parse5Element\n * @typedef {import('parse5').DefaultTreeAdapterMap['node']} Parse5Nodes\n * @typedef {import('parse5').DefaultTreeAdapterMap['documentType']} Parse5Doctype\n * @typedef {import('parse5').DefaultTreeAdapterMap['commentNode']} Parse5Comment\n * @typedef {import('parse5').DefaultTreeAdapterMap['textNode']} Parse5Text\n * @typedef {import('parse5').DefaultTreeAdapterMap['parentNode']} Parse5Parent\n * @typedef {import('parse5').Token.Attribute} Parse5Attribute\n *\n * @typedef {import('property-information').Schema} Schema\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {Space | null | undefined} [space='html']\n *   Which space the document is in (default: `'html'`).\n *\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it.\n *\n * @typedef {Exclude<Parse5Nodes, Parse5Document | Parse5Fragment>} Parse5Content\n *\n * @typedef {'html' | 'svg'} Space\n */\n\nimport {stringify as commas} from 'comma-separated-tokens'\nimport {ok as assert} from 'devlop'\nimport {find, html, svg} from 'property-information'\nimport {stringify as spaces} from 'space-separated-tokens'\nimport {webNamespaces} from 'web-namespaces'\nimport {zwitch} from 'zwitch'\n\n/** @type {Options} */\nconst emptyOptions = {}\n\nconst own = {}.hasOwnProperty\n\nconst one = zwitch('type', {handlers: {root, element, text, comment, doctype}})\n\n/**\n * Transform a hast tree to a `parse5` AST.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Parse5Nodes}\n *   `parse5` node.\n */\nexport function toParse5(tree, options) {\n  const settings = options || emptyOptions\n  const space = settings.space\n  return one(tree, space === 'svg' ? svg : html)\n}\n\n/**\n * @param {Root} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Document}\n *   Parse5 node.\n */\nfunction root(node, schema) {\n  /** @type {Parse5Document} */\n  const result = {\n    nodeName: '#document',\n    // @ts-expect-error: `parse5` uses enums, which are actually strings.\n    mode: (node.data || {}).quirksMode ? 'quirks' : 'no-quirks',\n    childNodes: []\n  }\n  result.childNodes = all(node.children, result, schema)\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Root} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Fragment}\n *   Parse5 node.\n */\nfunction fragment(node, schema) {\n  /** @type {Parse5Fragment} */\n  const result = {nodeName: '#document-fragment', childNodes: []}\n  result.childNodes = all(node.children, result, schema)\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Doctype} node\n *   Node (hast) to transform.\n * @returns {Parse5Doctype}\n *   Parse5 node.\n */\nfunction doctype(node) {\n  /** @type {Parse5Doctype} */\n  const result = {\n    nodeName: '#documentType',\n    name: 'html',\n    publicId: '',\n    systemId: '',\n    parentNode: null\n  }\n\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Text} node\n *   Node (hast) to transform.\n * @returns {Parse5Text}\n *   Parse5 node.\n */\nfunction text(node) {\n  /** @type {Parse5Text} */\n  const result = {\n    nodeName: '#text',\n    value: node.value,\n    parentNode: null\n  }\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Comment} node\n *   Node (hast) to transform.\n * @returns {Parse5Comment}\n *   Parse5 node.\n */\nfunction comment(node) {\n  /** @type {Parse5Comment} */\n  const result = {\n    nodeName: '#comment',\n    data: node.value,\n    parentNode: null\n  }\n\n  patch(node, result)\n\n  return result\n}\n\n/**\n * @param {Element} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Element}\n *   Parse5 node.\n */\nfunction element(node, schema) {\n  const parentSchema = schema\n  let currentSchema = parentSchema\n\n  if (\n    node.type === 'element' &&\n    node.tagName.toLowerCase() === 'svg' &&\n    parentSchema.space === 'html'\n  ) {\n    currentSchema = svg\n  }\n\n  /** @type {Array<Parse5Attribute>} */\n  const attrs = []\n  /** @type {string} */\n  let prop\n\n  if (node.properties) {\n    for (prop in node.properties) {\n      if (prop !== 'children' && own.call(node.properties, prop)) {\n        const result = createProperty(\n          currentSchema,\n          prop,\n          node.properties[prop]\n        )\n\n        if (result) {\n          attrs.push(result)\n        }\n      }\n    }\n  }\n\n  const space = currentSchema.space\n  // `html` and `svg` both have a space.\n  assert(space)\n\n  /** @type {Parse5Element} */\n  const result = {\n    nodeName: node.tagName,\n    tagName: node.tagName,\n    attrs,\n    // @ts-expect-error: `parse5` types are wrong.\n    namespaceURI: webNamespaces[space],\n    childNodes: [],\n    parentNode: null\n  }\n  result.childNodes = all(node.children, result, currentSchema)\n  patch(node, result)\n\n  if (node.tagName === 'template' && node.content) {\n    // @ts-expect-error: `parse5` types are wrong.\n    result.content = fragment(node.content, currentSchema)\n  }\n\n  return result\n}\n\n/**\n * Handle a property.\n *\n * @param {Schema} schema\n *   Current schema.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Parse5Attribute | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(schema, prop, value) {\n  const info = find(schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === false ||\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value)) ||\n    (!value && info.boolean)\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? commas(value) : spaces(value)\n  }\n\n  /** @type {Parse5Attribute} */\n  const attribute = {\n    name: info.attribute,\n    value: value === true ? '' : String(value)\n  }\n\n  if (info.space && info.space !== 'html' && info.space !== 'svg') {\n    const index = attribute.name.indexOf(':')\n\n    if (index < 0) {\n      attribute.prefix = ''\n    } else {\n      attribute.name = attribute.name.slice(index + 1)\n      attribute.prefix = info.attribute.slice(0, index)\n    }\n\n    attribute.namespace = webNamespaces[info.space]\n  }\n\n  return attribute\n}\n\n/**\n * Transform all hast nodes.\n *\n * @param {Array<RootContent>} children\n *   List of children.\n * @param {Parse5Parent} parentNode\n *   `parse5` parent node.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Array<Parse5Content>}\n *   Transformed children.\n */\nfunction all(children, parentNode, schema) {\n  let index = -1\n  /** @type {Array<Parse5Content>} */\n  const results = []\n\n  if (children) {\n    while (++index < children.length) {\n      /** @type {Parse5Content} */\n      const child = one(children[index], schema)\n\n      child.parentNode = parentNode\n\n      results.push(child)\n    }\n  }\n\n  return results\n}\n\n/**\n * Add position info from `from` to `to`.\n *\n * @param {Nodes} from\n *   hast node.\n * @param {Parse5Nodes} to\n *   `parse5` node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  const position = from.position\n\n  if (position && position.start && position.end) {\n    assert(typeof position.start.offset === 'number')\n    assert(typeof position.end.offset === 'number')\n\n    to.sourceCodeLocation = {\n      startLine: position.start.line,\n      startCol: position.start.column,\n      startOffset: position.start.offset,\n      endLine: position.end.line,\n      endCol: position.end.column,\n      endOffset: position.end.offset\n    }\n  }\n}\n", "/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nexport const htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'keygen',\n  'link',\n  'meta',\n  'param',\n  'source',\n  'track',\n  'wbr'\n]\n", "/**\n * @import {Options} from 'hast-util-raw'\n * @import {Comment, Doctype, Element, Nodes, RootContent, Root, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {DefaultTreeAdapterMap, ParserOptions} from 'parse5'\n * @import {Point} from 'unist'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Nodes) => undefined} handle\n *   Add a hast node to the parser.\n * @property {Options} options\n *   User configuration.\n * @property {Parser<DefaultTreeAdapterMap>} parser\n *   Current parser.\n * @property {boolean} stitches\n *   Whether there are stitches.\n */\n\n/**\n * @typedef Stitch\n *   Custom comment-like value we pass through parse5, which contains a\n *   replacement node that we’ll swap back in afterwards.\n * @property {'comment'} type\n *   Node type.\n * @property {{stitch: Nodes}} value\n *   Replacement value.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {fromParse5} from 'hast-util-from-parse5'\nimport {toParse5} from 'hast-util-to-parse5'\nimport {htmlVoidElements} from 'html-void-elements'\nimport {Parser, Token, TokenizerMode, html} from 'parse5'\nimport {pointEnd, pointStart} from 'unist-util-position'\nimport {visit} from 'unist-util-visit'\nimport {webNamespaces} from 'web-namespaces'\nimport {zwitch} from 'zwitch'\n\nconst gfmTagfilterExpression =\n  /<(\\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\\t\\n\\f\\r />])/gi\n\n// Node types associated with MDX.\n// <https://github.com/mdx-js/mdx/blob/8a56312/packages/mdx/lib/node-types.js>\nconst knownMdxNames = new Set([\n  'mdxFlowExpression',\n  'mdxJsxFlowElement',\n  'mdxJsxTextElement',\n  'mdxTextExpression',\n  'mdxjsEsm'\n])\n\n/** @type {ParserOptions<DefaultTreeAdapterMap>} */\nconst parseOptions = {sourceCodeLocationInfo: true, scriptingEnabled: false}\n\n/**\n * Pass a hast tree through an HTML parser, which will fix nesting, and turn\n * raw nodes into actual nodes.\n *\n * @param {Nodes} tree\n *   Original hast tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   Parsed again tree.\n */\nexport function raw(tree, options) {\n  const document = documentMode(tree)\n  /** @type {(node: Nodes, state: State) => undefined} */\n  const one = zwitch('type', {\n    handlers: {root, element, text, comment, doctype, raw: handleRaw},\n    unknown\n  })\n\n  /** @type {State} */\n  const state = {\n    parser: document\n      ? new Parser(parseOptions)\n      : Parser.getFragmentParser(undefined, parseOptions),\n    handle(node) {\n      one(node, state)\n    },\n    stitches: false,\n    options: options || {}\n  }\n\n  one(tree, state)\n  resetTokenizer(state, pointStart())\n\n  const p5 = document ? state.parser.document : state.parser.getFragment()\n  const result = fromParse5(p5, {\n    // To do: support `space`?\n    file: state.options.file\n  })\n\n  if (state.stitches) {\n    visit(result, 'comment', function (node, index, parent) {\n      const stitch = /** @type {Stitch} */ (/** @type {unknown} */ (node))\n      if (stitch.value.stitch && parent && index !== undefined) {\n        /** @type {Array<RootContent>} */\n        const siblings = parent.children\n        // @ts-expect-error: assume the stitch is allowed.\n        siblings[index] = stitch.value.stitch\n        return index\n      }\n    })\n  }\n\n  // Unpack if possible and when not given a `root`.\n  if (\n    result.type === 'root' &&\n    result.children.length === 1 &&\n    result.children[0].type === tree.type\n  ) {\n    return result.children[0]\n  }\n\n  return result\n}\n\n/**\n * Transform all nodes\n *\n * @param {Array<RootContent>} nodes\n *   hast content.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction all(nodes, state) {\n  let index = -1\n\n  /* istanbul ignore else - invalid nodes, see rehypejs/rehype-raw#7. */\n  if (nodes) {\n    while (++index < nodes.length) {\n      state.handle(nodes[index])\n    }\n  }\n}\n\n/**\n * Transform a root.\n *\n * @param {Root} node\n *   hast root node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction root(node, state) {\n  all(node.children, state)\n}\n\n/**\n * Transform an element.\n *\n * @param {Element} node\n *   hast element node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction element(node, state) {\n  startTag(node, state)\n\n  all(node.children, state)\n\n  endTag(node, state)\n}\n\n/**\n * Transform a text.\n *\n * @param {Text} node\n *   hast text node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction text(node, state) {\n  // Allow `DATA` through `PLAINTEXT`,\n  // but when hanging in a tag for example,\n  // switch back to `DATA`.\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  if (state.parser.tokenizer.state > 4) {\n    state.parser.tokenizer.state = 0\n  }\n\n  /** @type {Token.CharacterToken} */\n  const token = {\n    type: Token.TokenType.CHARACTER,\n    chars: node.value,\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, pointStart(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a doctype.\n *\n * @param {Doctype} node\n *   hast doctype node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction doctype(node, state) {\n  /** @type {Token.DoctypeToken} */\n  const token = {\n    type: Token.TokenType.DOCTYPE,\n    name: 'html',\n    forceQuirks: false,\n    publicId: '',\n    systemId: '',\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, pointStart(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a stitch.\n *\n * @param {Nodes} node\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction stitch(node, state) {\n  // Mark that there are stitches, so we need to walk the tree and revert them.\n  state.stitches = true\n\n  /** @type {Nodes} */\n  const clone = cloneWithoutChildren(node)\n\n  // Recurse, because to somewhat handle `[<x>]</x>` (where `[]` denotes the\n  // passed through node).\n  if ('children' in node && 'children' in clone) {\n    // Root in root out.\n    const fakeRoot = /** @type {Root} */ (\n      raw({type: 'root', children: node.children}, state.options)\n    )\n    clone.children = fakeRoot.children\n  }\n\n  // Hack: `value` is supposed to be a string, but as none of the tools\n  // (`parse5` or `hast-util-from-parse5`) looks at it, we can pass nodes\n  // through.\n  comment({type: 'comment', value: {stitch: clone}}, state)\n}\n\n/**\n * Transform a comment (or stitch).\n *\n * @param {Comment | Stitch} node\n *   hast comment node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction comment(node, state) {\n  /** @type {string} */\n  // @ts-expect-error: we pass stitches through.\n  const data = node.value\n\n  /** @type {Token.CommentToken} */\n  const token = {\n    type: Token.TokenType.COMMENT,\n    data,\n    location: createParse5Location(node)\n  }\n  resetTokenizer(state, pointStart(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a raw node.\n *\n * @param {Raw} node\n *   hast raw node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction handleRaw(node, state) {\n  // Reset preprocessor:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/preprocessor.ts#L18-L31>.\n  state.parser.tokenizer.preprocessor.html = ''\n  state.parser.tokenizer.preprocessor.pos = -1\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.lastGapPos = -2\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.gapStack = []\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.skipNextNewLine = false\n  state.parser.tokenizer.preprocessor.lastChunkWritten = false\n  state.parser.tokenizer.preprocessor.endOfChunkHit = false\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.isEol = false\n\n  // Now pass `node.value`.\n  setPoint(state, pointStart(node))\n\n  state.parser.tokenizer.write(\n    state.options.tagfilter\n      ? node.value.replace(gfmTagfilterExpression, '&lt;$1$2')\n      : node.value,\n    false\n  )\n  // @ts-expect-error: private.\n  state.parser.tokenizer._runParsingLoop()\n\n  // Character references hang, so if we ended there, we need to flush\n  // those too.\n  // We reset the preprocessor as if the document ends here.\n  // Then one single call to the relevant state does the trick, parse5\n  // consumes the whole token.\n\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  // Note: a change to `parse5`, which breaks this, was merged but not released.\n  // Investigate when it is.\n  // To do: remove next major.\n  /* c8 ignore next 12 -- removed in <https://github.com/inikulin/parse5/pull/897> */\n  if (\n    state.parser.tokenizer.state === 72 /* NAMED_CHARACTER_REFERENCE */ ||\n    // @ts-expect-error: removed.\n    state.parser.tokenizer.state === 78 /* NUMERIC_CHARACTER_REFERENCE_END */\n  ) {\n    state.parser.tokenizer.preprocessor.lastChunkWritten = true\n    /** @type {number} */\n    // @ts-expect-error: private.\n    const cp = state.parser.tokenizer._consume()\n    // @ts-expect-error: private.\n    state.parser.tokenizer._callState(cp)\n  }\n}\n\n/**\n * Crash on an unknown node.\n *\n * @param {unknown} node_\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Never.\n */\nfunction unknown(node_, state) {\n  const node = /** @type {Nodes} */ (node_)\n\n  if (\n    state.options.passThrough &&\n    state.options.passThrough.includes(node.type)\n  ) {\n    stitch(node, state)\n  } else {\n    let extra = ''\n\n    if (knownMdxNames.has(node.type)) {\n      extra =\n        \". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax\"\n    }\n\n    throw new Error('Cannot compile `' + node.type + '` node' + extra)\n  }\n}\n\n/**\n * Reset the tokenizer of a parser.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction resetTokenizer(state, point) {\n  setPoint(state, point)\n\n  // Process final characters if they’re still there after hibernating.\n  /** @type {Token.CharacterToken} */\n  // @ts-expect-error: private.\n  const token = state.parser.tokenizer.currentCharacterToken\n\n  if (token && token.location) {\n    token.location.endLine = state.parser.tokenizer.preprocessor.line\n    token.location.endCol = state.parser.tokenizer.preprocessor.col + 1\n    token.location.endOffset = state.parser.tokenizer.preprocessor.offset + 1\n    // @ts-expect-error: private.\n    state.parser.currentToken = token\n    // @ts-expect-error: private.\n    state.parser._processToken(state.parser.currentToken)\n  }\n\n  // Reset tokenizer:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/index.ts#L187-L223>.\n  // Especially putting it back in the `data` state is useful: some elements,\n  // like textareas and iframes, change the state.\n  // See GH-7.\n  // But also if broken HTML is in `raw`, and then a correct element is given.\n  // See GH-11.\n  // @ts-expect-error: private.\n  state.parser.tokenizer.paused = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.inLoop = false\n\n  // Note: don’t reset `state`, `inForeignNode`, or `lastStartTagName`, we\n  // manually update those when needed.\n  state.parser.tokenizer.active = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.returnState = TokenizerMode.DATA\n  // @ts-expect-error: private.\n  state.parser.tokenizer.charRefCode = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.consumedAfterSnapshot = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentLocation = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentCharacterToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentAttr = {name: '', value: ''}\n}\n\n/**\n * Set current location.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction setPoint(state, point) {\n  if (point && point.offset !== undefined) {\n    /** @type {Token.Location} */\n    const location = {\n      startLine: point.line,\n      startCol: point.column,\n      startOffset: point.offset,\n      endLine: -1,\n      endCol: -1,\n      endOffset: -1\n    }\n\n    // @ts-expect-error: private.\n    // type-coverage:ignore-next-line\n    state.parser.tokenizer.preprocessor.lineStartPos = -point.column + 1 // Looks weird, but ensures we get correct positional info.\n    state.parser.tokenizer.preprocessor.droppedBufferSize = point.offset\n    state.parser.tokenizer.preprocessor.line = point.line\n    // @ts-expect-error: private.\n    state.parser.tokenizer.currentLocation = location\n  }\n}\n\n/**\n * Emit a start tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction startTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, pointStart(node))\n\n  const current = state.parser.openElements.current\n  let ns = 'namespaceURI' in current ? current.namespaceURI : webNamespaces.html\n\n  if (ns === webNamespaces.html && tagName === 'svg') {\n    ns = webNamespaces.svg\n  }\n\n  const result = toParse5(\n    // Shallow clone to not delve into `children`: we only need the attributes.\n    {...node, children: []},\n    {space: ns === webNamespaces.svg ? 'svg' : 'html'}\n  )\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: Token.TokenType.START_TAG,\n    tagName,\n    tagID: html.getTagID(tagName),\n    // We always send start and end tags.\n    selfClosing: false,\n    ackSelfClosing: false,\n    // Always element.\n    /* c8 ignore next */\n    attrs: 'attrs' in result ? result.attrs : [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Set a tag name, similar to how the tokenizer would do it.\n  state.parser.tokenizer.lastStartTagName = tagName\n\n  // `inForeignNode` is correctly set by the parser.\n}\n\n/**\n * Emit an end tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction endTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n  // Do not emit closing tags for HTML void elements.\n  if (\n    !state.parser.tokenizer.inForeignNode &&\n    htmlVoidElements.includes(tagName)\n  ) {\n    return\n  }\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, pointEnd(node))\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: Token.TokenType.END_TAG,\n    tagName,\n    tagID: html.getTagID(tagName),\n    selfClosing: false,\n    ackSelfClosing: false,\n    attrs: [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Switch back to the data state after alternative states that don’t accept\n  // tags:\n  if (\n    // Current element is closed.\n    tagName === state.parser.tokenizer.lastStartTagName &&\n    // `<textarea>` and `<title>`\n    (state.parser.tokenizer.state === TokenizerMode.RCDATA ||\n      // `<iframe>`, `<noembed>`, `<noframes>`, `<style>`, `<xmp>`\n      state.parser.tokenizer.state === TokenizerMode.RAWTEXT ||\n      // `<script>`\n      state.parser.tokenizer.state === TokenizerMode.SCRIPT_DATA)\n    // Note: `<plaintext>` not needed, as it’s the last element.\n  ) {\n    state.parser.tokenizer.state = TokenizerMode.DATA\n  }\n}\n\n/**\n * Check if `node` represents a whole document or a fragment.\n *\n * @param {Nodes} node\n *   hast node.\n * @returns {boolean}\n *   Whether this represents a whole document or a fragment.\n */\nfunction documentMode(node) {\n  const head = node.type === 'root' ? node.children[0] : node\n  return Boolean(\n    head &&\n      (head.type === 'doctype' ||\n        (head.type === 'element' && head.tagName.toLowerCase() === 'html'))\n  )\n}\n\n/**\n * Get a `parse5` location from a node.\n *\n * @param {Nodes | Stitch} node\n *   hast node.\n * @returns {Token.Location}\n *   `parse5` location.\n */\nfunction createParse5Location(node) {\n  const start = pointStart(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n  const end = pointEnd(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n\n  /** @type {Record<keyof Token.Location, number | undefined>} */\n  const location = {\n    startLine: start.line,\n    startCol: start.column,\n    startOffset: start.offset,\n    endLine: end.line,\n    endCol: end.column,\n    endOffset: end.offset\n  }\n\n  // @ts-expect-error: unist point values can be `undefined` in hast, which\n  // `parse5` types don’t want.\n  return location\n}\n\n/**\n * @template {Nodes} NodeType\n *   Node type.\n * @param {NodeType} node\n *   Node to clone.\n * @returns {NodeType}\n *   Cloned node, without children.\n */\nfunction cloneWithoutChildren(node) {\n  return 'children' in node\n    ? structuredClone({...node, children: []})\n    : structuredClone(node)\n}\n", "/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast-util-raw').Options} RawOptions\n * @typedef {import('vfile').VFile} VFile\n */\n\n/**\n * @typedef {Omit<RawOptions, 'file'>} Options\n *   Configuration.\n */\n\nimport {raw} from 'hast-util-raw'\n\n/**\n * Parse the tree (and raw nodes) again, keeping positional info okay.\n *\n * @param {Options | null | undefined}  [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nexport default function rehypeRaw(options) {\n  /**\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {Root}\n   *   New tree.\n   */\n  return function (tree, file) {\n    // Assume root in -> root out.\n    const result = /** @type {Root} */ (raw(tree, {...options, file}))\n    return result\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAKO,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWlB,YAAY,UAAU,QAAQ,OAAO;AACnC,SAAK,SAAS;AACd,SAAK,WAAW;AAEhB,QAAI,OAAO;AACT,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;AAEA,OAAO,UAAU,SAAS,CAAC;AAC3B,OAAO,UAAU,WAAW,CAAC;AAC7B,OAAO,UAAU,QAAQ;;;ACdlB,SAAS,MAAM,aAAa,OAAO;AAExC,QAAM,WAAW,CAAC;AAElB,QAAM,SAAS,CAAC;AAEhB,aAAW,cAAc,aAAa;AACpC,WAAO,OAAO,UAAU,WAAW,QAAQ;AAC3C,WAAO,OAAO,QAAQ,WAAW,MAAM;AAAA,EACzC;AAEA,SAAO,IAAI,OAAO,UAAU,QAAQ,KAAK;AAC3C;;;ACjBO,SAAS,UAAU,OAAO;AAC/B,SAAO,MAAM,YAAY;AAC3B;;;ACNO,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAShB,YAAY,UAAU,WAAW;AAC/B,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AACF;AAEA,KAAK,UAAU,YAAY;AAC3B,KAAK,UAAU,aAAa;AAC5B,KAAK,UAAU,UAAU;AACzB,KAAK,UAAU,wBAAwB;AACvC,KAAK,UAAU,iBAAiB;AAChC,KAAK,UAAU,UAAU;AACzB,KAAK,UAAU,kBAAkB;AACjC,KAAK,UAAU,SAAS;AACxB,KAAK,UAAU,oBAAoB;AACnC,KAAK,UAAU,WAAW;AAC1B,KAAK,UAAU,iBAAiB;AAChC,KAAK,UAAU,QAAQ;;;AC/BvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAI,SAAS;AAEN,IAAM,UAAU,UAAU;AAC1B,IAAM,aAAa,UAAU;AAC7B,IAAM,oBAAoB,UAAU;AACpC,IAAM,SAAS,UAAU;AACzB,IAAM,iBAAiB,UAAU;AACjC,IAAM,iBAAiB,UAAU;AACjC,IAAM,wBAAwB,UAAU;AAE/C,SAAS,YAAY;AACnB,SAAO,KAAK,EAAE;AAChB;;;ACLA,IAAM;AAAA;AAAA,EACJ,OAAO,KAAK,aAAK;AAAA;AAGZ,IAAM,cAAN,cAA0B,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcpC,YAAY,UAAU,WAAW,MAAM,OAAO;AAC5C,QAAI,QAAQ;AAEZ,UAAM,UAAU,SAAS;AAEzB,SAAK,MAAM,SAAS,KAAK;AAEzB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,cAAM,QAAQ,OAAO,KAAK;AAC1B,aAAK,MAAM,OAAO,KAAK,IAAI,OAAO,cAAM,KAAK,OAAO,cAAM,KAAK,CAAC;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACF;AAEA,YAAY,UAAU,UAAU;AAchC,SAAS,KAAK,QAAQ,KAAK,OAAO;AAChC,MAAI,OAAO;AACT,WAAO,GAAG,IAAI;AAAA,EAChB;AACF;;;ACnBO,SAAS,OAAO,YAAY;AAEjC,QAAM,aAAa,CAAC;AAEpB,QAAM,UAAU,CAAC;AAEjB,aAAW,CAAC,UAAU,KAAK,KAAK,OAAO,QAAQ,WAAW,UAAU,GAAG;AACrE,UAAM,OAAO,IAAI;AAAA,MACf;AAAA,MACA,WAAW,UAAU,WAAW,cAAc,CAAC,GAAG,QAAQ;AAAA,MAC1D;AAAA,MACA,WAAW;AAAA,IACb;AAEA,QACE,WAAW,mBACX,WAAW,gBAAgB,SAAS,QAAQ,GAC5C;AACA,WAAK,kBAAkB;AAAA,IACzB;AAEA,eAAW,QAAQ,IAAI;AAEvB,YAAQ,UAAU,QAAQ,CAAC,IAAI;AAC/B,YAAQ,UAAU,KAAK,SAAS,CAAC,IAAI;AAAA,EACvC;AAEA,SAAO,IAAI,OAAO,YAAY,SAAS,WAAW,KAAK;AACzD;;;ACjEO,IAAM,OAAO,OAAO;AAAA,EACzB,YAAY;AAAA,IACV,sBAAsB;AAAA,IACtB,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM;AAAA,EACR;AAAA,EACA,UAAU,GAAG,UAAU;AACrB,WAAO,aAAa,SAChB,WACA,UAAU,SAAS,MAAM,CAAC,EAAE,YAAY;AAAA,EAC9C;AACF,CAAC;;;ACpDM,SAAS,uBAAuB,YAAY,WAAW;AAC5D,SAAO,aAAa,aAAa,WAAW,SAAS,IAAI;AAC3D;;;ACAO,SAAS,yBAAyB,YAAY,UAAU;AAC7D,SAAO,uBAAuB,YAAY,SAAS,YAAY,CAAC;AAClE;;;ACDO,IAAM,OAAO,OAAO;AAAA,EACzB,YAAY;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,iBAAiB,CAAC,WAAW,YAAY,SAAS,UAAU;AAAA,EAC5D,YAAY;AAAA;AAAA,IAEV,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,QAAQ,SAAS;AAAA,IACjB,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,KAAK;AAAA,IACL,UAAU;AAAA,IACV,KAAK;AAAA,IACL,WAAW;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,2BAA2B;AAAA,IAC3B,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,sBAAsB;AAAA,IACtB,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA,IACT,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,KAAK;AAAA,IACL,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,gBAAgB;AAAA,IAChB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,oBAAoB;AAAA;AAAA;AAAA,IAIpB,OAAO;AAAA;AAAA,IACP,OAAO;AAAA;AAAA,IACP,SAAS;AAAA;AAAA,IACT,MAAM;AAAA;AAAA,IACN,YAAY;AAAA;AAAA,IACZ,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,aAAa;AAAA;AAAA,IACb,aAAa;AAAA;AAAA,IACb,MAAM;AAAA;AAAA,IACN,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,OAAO;AAAA;AAAA,IACP,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,OAAO;AAAA;AAAA,IACP,aAAa;AAAA;AAAA,IACb,QAAQ;AAAA;AAAA,IACR,YAAY;AAAA;AAAA,IACZ,MAAM;AAAA;AAAA,IACN,UAAU;AAAA;AAAA,IACV,QAAQ;AAAA;AAAA,IACR,cAAc;AAAA;AAAA,IACd,aAAa;AAAA;AAAA,IACb,UAAU;AAAA;AAAA,IACV,QAAQ;AAAA;AAAA,IACR,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,KAAK;AAAA;AAAA,IACL,aAAa;AAAA;AAAA,IACb,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,WAAW;AAAA;AAAA,IACX,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,MAAM;AAAA;AAAA,IACN,WAAW;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,IACX,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA;AAAA,IAGR,mBAAmB;AAAA,IACnB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,yBAAyB;AAAA,IACzB,uBAAuB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,EACP,WAAW;AACb,CAAC;;;ACvTM,IAAM,MAAM,OAAO;AAAA,EACxB,YAAY;AAAA,IACV,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,UAAU;AAAA,IACV,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA;AAAA,IAET,eAAe;AAAA,IACf,eAAe;AAAA,EACjB;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,SAAS;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,eAAe;AAAA,IACf,aAAa;AAAA,IACb,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,MAAM;AAAA,IACN,UAAU;AAAA,IACV,eAAe;AAAA,IACf,UAAU;AAAA,IACV,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,UAAU;AAAA,IACV,eAAe;AAAA,IACf,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,KAAK;AAAA,IACL,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,UAAU;AAAA,IACV,2BAA2B;AAAA,IAC3B,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,IAAI;AAAA,IACJ,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,WAAW;AAAA,IACX,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,UAAU;AAAA;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,aAAa;AAAA,IACb,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,KAAK;AAAA,IACL,OAAO;AAAA,IACP,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,OAAO;AAAA,IACP,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,OAAO;AAAA,IACP,cAAc;AAAA,IACd,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,OAAO;AAAA,IACP,mBAAmB;AAAA,IACnB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,eAAe;AAAA,IACf,cAAc;AAAA,IACd,UAAU;AAAA,IACV,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,GAAG;AAAA,IACH,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,EACP,WAAW;AACb,CAAC;;;ACpjBM,IAAM,QAAQ,OAAO;AAAA,EAC1B,YAAY;AAAA,IACV,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,EACP,UAAU,GAAG,UAAU;AACrB,WAAO,WAAW,SAAS,MAAM,CAAC,EAAE,YAAY;AAAA,EAClD;AACF,CAAC;;;ACbM,IAAM,QAAQ,OAAO;AAAA,EAC1B,YAAY,EAAC,YAAY,cAAa;AAAA,EACtC,YAAY,EAAC,YAAY,MAAM,OAAO,KAAI;AAAA,EAC1C,OAAO;AAAA,EACP,WAAW;AACb,CAAC;;;ACNM,IAAM,MAAM,OAAO;AAAA,EACxB,YAAY,EAAC,SAAS,MAAM,SAAS,MAAM,UAAU,KAAI;AAAA,EACzD,OAAO;AAAA,EACP,UAAU,GAAG,UAAU;AACrB,WAAO,SAAS,SAAS,MAAM,CAAC,EAAE,YAAY;AAAA,EAChD;AACF,CAAC;;;ACAD,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,QAAQ;AAgCP,SAAS,KAAK,QAAQ,OAAO;AAClC,QAAM,SAAS,UAAU,KAAK;AAC9B,MAAI,WAAW;AACf,MAAI,OAAO;AAEX,MAAI,UAAU,OAAO,QAAQ;AAC3B,WAAO,OAAO,SAAS,OAAO,OAAO,MAAM,CAAC;AAAA,EAC9C;AAEA,MAAI,OAAO,SAAS,KAAK,OAAO,MAAM,GAAG,CAAC,MAAM,UAAU,MAAM,KAAK,KAAK,GAAG;AAE3E,QAAI,MAAM,OAAO,CAAC,MAAM,KAAK;AAE3B,YAAM,OAAO,MAAM,MAAM,CAAC,EAAE,QAAQ,MAAM,SAAS;AACnD,iBAAW,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,IACjE,OAAO;AAEL,YAAM,OAAO,MAAM,MAAM,CAAC;AAE1B,UAAI,CAAC,KAAK,KAAK,IAAI,GAAG;AACpB,YAAI,SAAS,KAAK,QAAQ,KAAK,KAAK;AAEpC,YAAI,OAAO,OAAO,CAAC,MAAM,KAAK;AAC5B,mBAAS,MAAM;AAAA,QACjB;AAEA,gBAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,KAAK,UAAU,KAAK;AACjC;AAQA,SAAS,MAAM,IAAI;AACjB,SAAO,MAAM,GAAG,YAAY;AAC9B;AAQA,SAAS,UAAU,IAAI;AACrB,SAAO,GAAG,OAAO,CAAC,EAAE,YAAY;AAClC;;;ACrFO,IAAMA,QAAO,MAAM,CAAC,MAAM,MAAU,OAAO,OAAO,GAAG,GAAG,MAAM;AAK9D,IAAMC,OAAM,MAAM,CAAC,MAAM,KAAS,OAAO,OAAO,GAAG,GAAG,KAAK;;;ACM3D,SAAS,MAAM,OAAO;AAE3B,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ,OAAO,SAAS,EAAE;AAChC,MAAI,QAAQ,MAAM,QAAQ,GAAG;AAC7B,MAAI,QAAQ;AAEZ,MAAI,MAAM;AAEV,SAAO,CAAC,KAAK;AACX,QAAI,UAAU,IAAI;AAChB,cAAQ,MAAM;AACd,YAAM;AAAA,IACR;AAEA,UAAM,QAAQ,MAAM,MAAM,OAAO,KAAK,EAAE,KAAK;AAE7C,QAAI,SAAS,CAAC,KAAK;AACjB,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,YAAQ,QAAQ;AAChB,YAAQ,MAAM,QAAQ,KAAK,KAAK;AAAA,EAClC;AAEA,SAAO;AACT;AAYO,SAAS,UAAU,QAAQ,SAAS;AACzC,QAAM,WAAW,WAAW,CAAC;AAG7B,QAAM,QAAQ,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,QAAQ,EAAE,IAAI;AAEnE,SAAO,MACJ;AAAA,KACE,SAAS,WAAW,MAAM,MACzB,OACC,SAAS,YAAY,QAAQ,KAAK;AAAA,EACvC,EACC,KAAK;AACV;;;ACjDA,IAAM,SAAS;AAoBR,SAAS,cAAc,UAAU,gBAAgB;AACtD,QAAM,QAAQ,YAAY;AAE1B,QAAM,QAAQ,CAAC;AACf,MAAI,QAAQ;AAEZ,MAAI;AAEJ,MAAI;AAEJ,SAAO,QAAQ,MAAM,QAAQ;AAC3B,WAAO,YAAY;AACnB,UAAM,QAAQ,OAAO,KAAK,KAAK;AAC/B,UAAM,WAAW,MAAM,MAAM,OAAO,QAAQ,MAAM,QAAQ,MAAM,MAAM;AAEtE,QAAI,UAAU;AACZ,UAAI,CAAC,UAAU;AACb,kBAAU;AAAA,MACZ,WAAW,aAAa,KAAK;AAC3B,cAAM,KAAK;AAAA,MACb,WAAW,MAAM,QAAQ,MAAM,SAAS,GAAG;AACzC,cAAM,UAAU,KAAK,QAAQ;AAAA,MAC/B,OAAO;AACL,cAAM,YAAY,CAAC,QAAQ;AAAA,MAC7B;AAEA,eAAS,SAAS;AAAA,IACpB;AAEA,QAAI,OAAO;AACT,iBAAW,MAAM,CAAC;AAClB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,MAAM;AAAA;AAAA,IAEN,SAAS,WAAW,kBAAkB;AAAA,IACtC,YAAY;AAAA,IACZ,UAAU,CAAC;AAAA,EACb;AACF;;;AC9EO,SAASC,OAAM,OAAO;AAC3B,QAAM,QAAQ,OAAO,SAAS,EAAE,EAAE,KAAK;AACvC,SAAO,QAAQ,MAAM,MAAM,eAAe,IAAI,CAAC;AACjD;AAUO,SAASC,WAAU,QAAQ;AAChC,SAAO,OAAO,KAAK,GAAG,EAAE,KAAK;AAC/B;;;ACoDO,SAAS,QAAQ,QAAQ,gBAAgB,eAAe;AAC7D,QAAM,SAAS,gBAAgB,gBAAgB,aAAa,IAAI;AA8BhE,WAASC,GAAE,UAAU,eAAe,UAAU;AAE5C,QAAI;AAEJ,QAAI,aAAa,QAAQ,aAAa,QAAW;AAC/C,aAAO,EAAC,MAAM,QAAQ,UAAU,CAAC,EAAC;AAElC,YAAM;AAAA;AAAA,QAA8B;AAAA;AACpC,eAAS,QAAQ,KAAK;AAAA,IACxB,OAAO;AACL,aAAO,cAAc,UAAU,cAAc;AAE7C,YAAM,QAAQ,KAAK,QAAQ,YAAY;AACvC,YAAM,WAAW,SAAS,OAAO,IAAI,KAAK,IAAI;AAC9C,WAAK,UAAU,YAAY;AAG3B,UAAI,QAAQ,UAAU,GAAG;AACvB,iBAAS,QAAQ,UAAU;AAAA,MAC7B,OAAO;AACL,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,UAAU,GAAG;AACrD,sBAAY,QAAQ,KAAK,YAAY,KAAK,KAAK;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAGA,eAAW,SAAS,UAAU;AAC5B,eAAS,KAAK,UAAU,KAAK;AAAA,IAC/B;AAEA,QAAI,KAAK,SAAS,aAAa,KAAK,YAAY,YAAY;AAC1D,WAAK,UAAU,EAAC,MAAM,QAAQ,UAAU,KAAK,SAAQ;AACrD,WAAK,WAAW,CAAC;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AAEA,SAAOA;AACT;AAUA,SAAS,QAAQ,OAAO;AAEtB,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,GAAG;AACvE,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,SAAS,SAAU,QAAO;AAI3C,QAAM;AAAA;AAAA,IAAiD;AAAA;AACvD,QAAM,OAAO,OAAO,KAAK,KAAK;AAE9B,aAAW,OAAO,MAAM;AACtB,UAAMC,SAAQ,OAAO,GAAG;AAExB,QAAIA,UAAS,OAAOA,WAAU,UAAU;AACtC,UAAI,CAAC,MAAM,QAAQA,MAAK,EAAG,QAAO;AAElC,YAAM;AAAA;AAAA,QAA8CA;AAAA;AAEpD,iBAAW,QAAQ,MAAM;AACvB,YAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACxD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,MAAI,cAAc,SAAS,MAAM,QAAQ,MAAM,QAAQ,GAAG;AACxD,WAAO;AAAA,EACT;AAKA,SAAO;AACT;AAcA,SAAS,YAAY,QAAQ,YAAY,KAAK,OAAO;AACnD,QAAM,OAAO,KAAK,QAAQ,GAAG;AAE7B,MAAI;AAGJ,MAAI,UAAU,QAAQ,UAAU,OAAW;AAE3C,MAAI,OAAO,UAAU,UAAU;AAE7B,QAAI,OAAO,MAAM,KAAK,EAAG;AAEzB,aAAS;AAAA,EACX,WAES,OAAO,UAAU,WAAW;AACnC,aAAS;AAAA,EACX,WAES,OAAO,UAAU,UAAU;AAClC,QAAI,KAAK,gBAAgB;AACvB,eAASC,OAAY,KAAK;AAAA,IAC5B,WAAW,KAAK,gBAAgB;AAC9B,eAAS,MAAY,KAAK;AAAA,IAC5B,WAAW,KAAK,uBAAuB;AACrC,eAASA,OAAY,MAAY,KAAK,EAAE,KAAK,GAAG,CAAC;AAAA,IACnD,OAAO;AACL,eAAS,eAAe,MAAM,KAAK,UAAU,KAAK;AAAA,IACpD;AAAA,EACF,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,aAAS,CAAC,GAAG,KAAK;AAAA,EACpB,OAAO;AACL,aAAS,KAAK,aAAa,UAAU,MAAM,KAAK,IAAI,OAAO,KAAK;AAAA,EAClE;AAEA,MAAI,MAAM,QAAQ,MAAM,GAAG;AAEzB,UAAM,cAAc,CAAC;AAErB,eAAW,QAAQ,QAAQ;AAEzB,kBAAY;AAAA;AAAA,QAER,eAAe,MAAM,KAAK,UAAU,IAAI;AAAA,MAE5C;AAAA,IACF;AAEA,aAAS;AAAA,EACX;AAGA,MAAI,KAAK,aAAa,eAAe,MAAM,QAAQ,WAAW,SAAS,GAAG;AAExE,aAAS,WAAW,UAAU;AAAA;AAAA,MAC6B;AAAA,IAC3D;AAAA,EACF;AAEA,aAAW,KAAK,QAAQ,IAAI;AAC9B;AAUA,SAAS,SAAS,OAAO,OAAO;AAC9B,MAAI,UAAU,QAAQ,UAAU,QAAW;AAAA,EAE3C,WAAW,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AACjE,UAAM,KAAK,EAAC,MAAM,QAAQ,OAAO,OAAO,KAAK,EAAC,CAAC;AAAA,EACjD,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,eAAW,SAAS,OAAO;AACzB,eAAS,OAAO,KAAK;AAAA,IACvB;AAAA,EACF,WAAW,OAAO,UAAU,YAAY,UAAU,OAAO;AACvD,QAAI,MAAM,SAAS,QAAQ;AACzB,eAAS,OAAO,MAAM,QAAQ;AAAA,IAChC,OAAO;AACL,YAAM,KAAK,KAAK;AAAA,IAClB;AAAA,EACF,OAAO;AACL,UAAM,IAAI,MAAM,2CAA2C,QAAQ,GAAG;AAAA,EACxE;AACF;AAcA,SAAS,eAAe,MAAM,MAAM,OAAO;AACzC,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,KAAK,UAAU,SAAS,CAAC,OAAO,MAAM,OAAO,KAAK,CAAC,GAAG;AACxD,aAAO,OAAO,KAAK;AAAA,IACrB;AAEA,SACG,KAAK,WAAW,KAAK,uBACrB,UAAU,MAAM,UAAU,KAAK,MAAM,UAAU,IAAI,IACpD;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAAS,MAAM,QAAQ;AAErB,QAAM,SAAS,CAAC;AAEhB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,WAAO,KAAK,CAAC,KAAK,KAAK,EAAE,KAAK,IAAI,CAAC;AAAA,EACrC;AAEA,SAAO,OAAO,KAAK,IAAI;AACzB;AAUA,SAAS,gBAAgB,QAAQ;AAE/B,QAAM,SAAS,oBAAI,IAAI;AAEvB,aAAW,SAAS,QAAQ;AAC1B,WAAO,IAAI,MAAM,YAAY,GAAG,KAAK;AAAA,EACvC;AAEA,SAAO;AACT;;;ACvWO,IAAM,2BAA2B;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACvBO,IAAM,IAAI,QAAQC,OAAM,KAAK;AAI7B,IAAM,IAAI,QAAQC,MAAK,KAAK,wBAAwB;;;ACRpD,SAAS,SAAS,MAAM;AAC7B,QAAM,QAAQ,OAAO,IAAI;AAOzB,QAAM,UAAU,CAAC;AAEjB,SAAO,EAAC,UAAU,QAAO;AAGzB,WAAS,QAAQ,QAAQ;AACvB,QAAI,OAAO,WAAW,YAAY,SAAS,MAAM,UAAU,MAAM,QAAQ;AACvE,UAAI,QAAQ;AAEZ,aAAO,MAAM;AACX,YAAI,MAAM,QAAQ,KAAK;AAEvB,YAAI,QAAQ,QAAW;AACrB,gBAAM,MAAM,KAAK,OAAO,QAAQ,QAAQ,CAAC,CAAC;AAC1C,gBAAM,QAAQ,KAAK,MAAM,SAAS,IAAI,MAAM;AAC5C,kBAAQ,KAAK,IAAI;AAAA,QACnB;AAEA,YAAI,MAAM,QAAQ;AAChB,iBAAO;AAAA,YACL,MAAM,QAAQ;AAAA,YACd,QAAQ,UAAU,QAAQ,IAAI,QAAQ,QAAQ,CAAC,IAAI,KAAK;AAAA,YACxD;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,WAAS,SAASC,QAAO;AACvB,QACEA,UACA,OAAOA,OAAM,SAAS,YACtB,OAAOA,OAAM,WAAW,YACxB,CAAC,OAAO,MAAMA,OAAM,IAAI,KACxB,CAAC,OAAO,MAAMA,OAAM,MAAM,GAC1B;AACA,aAAO,QAAQ,SAASA,OAAM,MAAM;AAClC,cAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC;AACvC,cAAM,MAAM,KAAK,OAAO,IAAI;AAC5B,cAAM,MAAM,QAAQ,KAAK,MAAM,SAAS,IAAI,MAAM;AAClD,YAAI,SAAS,IAAK;AAClB,gBAAQ,KAAK,GAAG;AAAA,MAClB;AAEA,YAAM,UACHA,OAAM,OAAO,IAAI,QAAQA,OAAM,OAAO,CAAC,IAAI,KAAKA,OAAM,SAAS;AAElE,UAAI,SAAS,QAAQA,OAAM,OAAO,CAAC,EAAG,QAAO;AAAA,IAC/C;AAAA,EACF;AACF;AAMA,SAAS,KAAK,OAAO,MAAM;AACzB,QAAM,KAAK,MAAM,QAAQ,MAAM,IAAI;AACnC,QAAM,KAAK,MAAM,QAAQ,MAAM,IAAI;AACnC,MAAI,OAAO,GAAI,QAAO;AACtB,MAAI,OAAO,MAAM,KAAK,MAAM,GAAI,QAAO;AACvC,SAAO,KAAK,KAAK,KAAK;AACxB;;;ACvFO,IAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;;;ACgBA,IAAM,MAAM,CAAC,EAAE;AAGf,IAAM,QAAQ,OAAO;AAYd,SAAS,WAAW,MAAM,SAAS;AACxC,QAAM,WAAW,WAAW,CAAC;AAE7B,SAAO;AAAA,IACL;AAAA,MACE,MAAM,SAAS,QAAQ;AAAA,MACvB,UAAU;AAAA,MACV,QAAQ,SAAS,UAAU,QAAQC,OAAMC;AAAA,MACzC,SAAS,SAAS,WAAW;AAAA,IAC/B;AAAA,IACA;AAAA,EACF;AACF;AAYA,SAAS,IAAI,OAAO,MAAM;AAExB,MAAI;AAEJ,UAAQ,KAAK,UAAU;AAAA,IACrB,KAAK,YAAY;AACf,YAAM;AAAA;AAAA,QACJ;AAAA;AAEF,eAAS,EAAC,MAAM,WAAW,OAAO,UAAU,KAAI;AAChD,YAAM,OAAO,WAAW,MAAM;AAC9B,aAAO;AAAA,IACT;AAAA,IAEA,KAAK;AAAA,IACL,KAAK,sBAAsB;AACzB,YAAM;AAAA;AAAA,QAEF;AAAA;AAEJ,YAAM,aACJ,UAAU,YACN,UAAU,SAAS,YAAY,UAAU,SAAS,mBAClD;AAEN,eAAS;AAAA,QACP,MAAM;AAAA,QACN,UAAU,IAAI,OAAO,KAAK,UAAU;AAAA,QACpC,MAAM,EAAC,WAAU;AAAA,MACnB;AAEA,UAAI,MAAM,QAAQ,MAAM,UAAU;AAChC,cAAM,WAAW,OAAO,MAAM,IAAI;AAClC,cAAM,MAAM,SAAS,QAAQ;AAC7B,cAAM,QAAQ,IAAI,QAAQ,CAAC;AAC3B,cAAM,MAAM,IAAI,QAAQ,SAAS,MAAM;AAEvC,WAAO,OAAO,kBAAkB;AAChC,WAAO,KAAK,gBAAgB;AAC5B,eAAO,WAAW,EAAC,OAAO,IAAG;AAAA,MAC/B;AAEA,aAAO;AAAA,IACT;AAAA,IAEA,KAAK,iBAAiB;AACpB,YAAM;AAAA;AAAA,QACJ;AAAA;AAEF,eAAS,EAAC,MAAM,UAAS;AACzB,YAAM,OAAO,WAAW,MAAM;AAC9B,aAAO;AAAA,IACT;AAAA,IAEA,KAAK,SAAS;AACZ,YAAM;AAAA;AAAA,QAA8D;AAAA;AACpE,eAAS,EAAC,MAAM,QAAQ,OAAO,UAAU,MAAK;AAC9C,YAAM,OAAO,WAAW,MAAM;AAC9B,aAAO;AAAA,IACT;AAAA;AAAA,IAGA,SAAS;AACP,YAAM;AAAA;AAAA,QAA6D;AAAA;AACnE,eAAS,QAAQ,OAAO,SAAS;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAYA,SAAS,IAAI,OAAO,OAAO;AACzB,MAAI,QAAQ;AAEZ,QAAM,UAAU,CAAC;AAEjB,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAE7B,UAAM;AAAA;AAAA,MAAqC,IAAI,OAAO,MAAM,KAAK,CAAC;AAAA;AAClE,YAAQ,KAAK,MAAM;AAAA,EACrB;AAEA,SAAO;AACT;AAYA,SAAS,QAAQ,OAAO,MAAM;AAC5B,QAAM,SAAS,MAAM;AAErB,QAAM,SAAS,KAAK,iBAAiB,cAAc,MAAMD,OAAMC;AAG/D,MAAI,QAAQ;AAEZ,QAAM,aAAa,CAAC;AAEpB,SAAO,EAAE,QAAQ,KAAK,MAAM,QAAQ;AAClC,UAAM,YAAY,KAAK,MAAM,KAAK;AAClC,UAAM,QACH,UAAU,SAAS,UAAU,SAAS,MAAM,MAAM,UAAU;AAC/D,QAAI,CAAC,IAAI,KAAK,OAAO,IAAI,GAAG;AAC1B,iBAAW,IAAI,IAAI,UAAU;AAAA,IAC/B;AAAA,EACF;AAGA,QAAM,IAAI,MAAM,OAAO,UAAU,QAAQ,IAAI;AAC7C,QAAM,SAAS,EAAE,KAAK,SAAS,YAAY,IAAI,OAAO,KAAK,UAAU,CAAC;AACtE,QAAM,OAAO,MAAM,MAAM;AAGzB,MAAI,OAAO,YAAY,YAAY;AACjC,UAAM;AAAA;AAAA,MAA8D;AAAA;AACpE,UAAM,MAAM,UAAU;AACtB,UAAMC,YAAW,OAAO,IAAI,YAAY,SAAS,IAAI,QAAQ;AAC7D,UAAMC,UAAS,OAAO,IAAI,UAAU,SAAS,IAAI,MAAM;AAGvD,UAAM;AAAA;AAAA,MAA+B,IAAI,OAAO,UAAU,OAAO;AAAA;AAEjE,QAAID,aAAYC,WAAU,MAAM,MAAM;AACpC,cAAQ,WAAW,EAAC,OAAOD,UAAS,KAAK,KAAKC,QAAO,MAAK;AAAA,IAC5D;AAEA,WAAO,UAAU;AAAA,EACnB;AAEA,QAAM,SAAS;AAEf,SAAO;AACT;AAcA,SAAS,MAAM,OAAO,MAAM,IAAI;AAC9B,MAAI,wBAAwB,QAAQ,KAAK,sBAAsB,MAAM,MAAM;AACzE,UAAMC,YAAW,eAAe,OAAO,IAAI,KAAK,kBAAkB;AAElE,QAAIA,WAAU;AACZ,YAAM,WAAW;AACjB,SAAG,WAAWA;AAAA,IAChB;AAAA,EACF;AACF;AAcA,SAAS,eAAe,OAAO,MAAMC,WAAU;AAC7C,QAAM,SAAS,SAASA,SAAQ;AAEhC,MAAI,KAAK,SAAS,WAAW;AAC3B,UAAM,OAAO,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAInD,QACE,UACA,CAACA,UAAS,UACV,QACA,KAAK,YACL,KAAK,SAAS,KACd;AACA,aAAO,MAAM,OAAO,OAAO,CAAC,GAAG,KAAK,SAAS,GAAG;AAAA,IAClD;AAEA,QAAI,MAAM,SAAS;AAEjB,YAAM,aAAa,CAAC;AAEpB,UAAI;AAEJ,UAAIA,UAAS,OAAO;AAClB,aAAK,OAAOA,UAAS,OAAO;AAC1B,cAAI,IAAI,KAAKA,UAAS,OAAO,GAAG,GAAG;AACjC,uBAAW,KAAK,MAAM,QAAQ,GAAG,EAAE,QAAQ,IAAI;AAAA,cAC7CA,UAAS,MAAM,GAAG;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,SAAOA,UAAS,UAAU,0BAA0B;AACpD,YAAM,UAAU,SAASA,UAAS,QAAQ;AAC1C,YAAM,UAAUA,UAAS,SAAS,SAASA,UAAS,MAAM,IAAI;AAE9D,YAAM,OAAO,EAAC,QAAO;AACrB,UAAI,QAAS,MAAK,UAAU;AAC5B,WAAK,aAAa;AAElB,WAAK,OAAO,EAAC,UAAU,KAAI;AAAA,IAC7B;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAAS,SAAS,KAAK;AACrB,QAAM,QAAQ,MAAM;AAAA,IAClB,MAAM,IAAI;AAAA,IACV,QAAQ,IAAI;AAAA,IACZ,QAAQ,IAAI;AAAA,EACd,CAAC;AACD,QAAM,MAAM,MAAM;AAAA,IAChB,MAAM,IAAI;AAAA,IACV,QAAQ,IAAI;AAAA,IACZ,QAAQ,IAAI;AAAA,EACd,CAAC;AAID,SAAO,SAAS,MAAM,EAAC,OAAO,IAAG,IAAI;AACvC;AAUA,SAAS,MAAMC,QAAO;AACpB,SAAOA,OAAM,QAAQA,OAAM,SAASA,SAAQ;AAC9C;;;AC1UO,IAAMC,UAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,YAAY,UAAU,QAAQ,OAAO;AACnC,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,QAAI,OAAO;AACT,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;AAGAA,QAAO,UAAU,WAAW,CAAC;AAE7BA,QAAO,UAAU,SAAS,CAAC;AAE3BA,QAAO,UAAU,QAAQ;;;ACflB,SAASC,OAAM,aAAa,OAAO;AAExC,QAAM,WAAW,CAAC;AAElB,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,YAAY,QAAQ;AACnC,WAAO,OAAO,UAAU,YAAY,KAAK,EAAE,QAAQ;AACnD,WAAO,OAAO,QAAQ,YAAY,KAAK,EAAE,MAAM;AAAA,EACjD;AAEA,SAAO,IAAIC,QAAO,UAAU,QAAQ,KAAK;AAC3C;;;ACrBO,SAASC,WAAU,OAAO;AAC/B,SAAO,MAAM,YAAY;AAC3B;;;ACNO,IAAMC,QAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,YAAY,UAAU,WAAW;AAE/B,SAAK,WAAW;AAEhB,SAAK,YAAY;AAAA,EACnB;AACF;AAGAA,MAAK,UAAU,QAAQ;AACvBA,MAAK,UAAU,UAAU;AACzBA,MAAK,UAAU,aAAa;AAC5BA,MAAK,UAAU,oBAAoB;AACnCA,MAAK,UAAU,SAAS;AACxBA,MAAK,UAAU,iBAAiB;AAChCA,MAAK,UAAU,iBAAiB;AAChCA,MAAK,UAAU,wBAAwB;AACvCA,MAAK,UAAU,kBAAkB;AACjCA,MAAK,UAAU,UAAU;;;ACxBzB,IAAAC,iBAAA;AAAA,SAAAA,gBAAA;AAAA,iBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,6BAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,sBAAAC;AAAA;AAAA,IAAIC,UAAS;AAEN,IAAMP,WAAUQ,WAAU;AAC1B,IAAMP,cAAaO,WAAU;AAC7B,IAAMH,qBAAoBG,WAAU;AACpC,IAAMJ,UAASI,WAAU;AACzB,IAAMF,kBAAiBE,WAAU;AACjC,IAAML,kBAAiBK,WAAU;AACjC,IAAMN,yBAAwBM,WAAU;AAE/C,SAASA,aAAY;AACnB,SAAO,KAAK,EAAED;AAChB;;;ACPA,IAAME,UAAS,OAAO,KAAKC,cAAK;AAEzB,IAAMC,eAAN,cAA0BC,MAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpC,YAAY,UAAU,WAAW,MAAM,OAAO;AAC5C,QAAI,QAAQ;AAEZ,UAAM,UAAU,SAAS;AAEzB,IAAAC,MAAK,MAAM,SAAS,KAAK;AAEzB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,EAAE,QAAQJ,QAAO,QAAQ;AAC9B,cAAM,QAAQA,QAAO,KAAK;AAC1B,QAAAI,MAAK,MAAMJ,QAAO,KAAK,IAAI,OAAOC,eAAM,KAAK,OAAOA,eAAM,KAAK,CAAC;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACF;AAEAC,aAAY,UAAU,UAAU;AAOhC,SAASE,MAAK,QAAQ,KAAK,OAAO;AAChC,MAAI,OAAO;AAET,WAAO,GAAG,IAAI;AAAA,EAChB;AACF;;;ACzBA,IAAMC,OAAM,CAAC,EAAE;AAMR,SAASC,QAAO,YAAY;AAEjC,QAAM,WAAW,CAAC;AAElB,QAAM,SAAS,CAAC;AAEhB,MAAI;AAEJ,OAAK,QAAQ,WAAW,YAAY;AAClC,QAAID,KAAI,KAAK,WAAW,YAAY,IAAI,GAAG;AACzC,YAAM,QAAQ,WAAW,WAAW,IAAI;AACxC,YAAM,OAAO,IAAIE;AAAA,QACf;AAAA,QACA,WAAW,UAAU,WAAW,cAAc,CAAC,GAAG,IAAI;AAAA,QACtD;AAAA,QACA,WAAW;AAAA,MACb;AAEA,UACE,WAAW,mBACX,WAAW,gBAAgB,SAAS,IAAI,GACxC;AACA,aAAK,kBAAkB;AAAA,MACzB;AAEA,eAAS,IAAI,IAAI;AAEjB,aAAOC,WAAU,IAAI,CAAC,IAAI;AAC1B,aAAOA,WAAU,KAAK,SAAS,CAAC,IAAI;AAAA,IACtC;AAAA,EACF;AAEA,SAAO,IAAIC,QAAO,UAAU,QAAQ,WAAW,KAAK;AACtD;;;ACvDO,IAAMC,SAAQC,QAAO;AAAA,EAC1B,OAAO;AAAA,EACP,UAAU,GAAG,MAAM;AACjB,WAAO,WAAW,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,EAC9C;AAAA,EACA,YAAY;AAAA,IACV,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACF,CAAC;;;ACdM,IAAMC,OAAMC,QAAO;AAAA,EACxB,OAAO;AAAA,EACP,UAAU,GAAG,MAAM;AACjB,WAAO,SAAS,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,EAC5C;AAAA,EACA,YAAY,EAAC,SAAS,MAAM,SAAS,MAAM,UAAU,KAAI;AAC3D,CAAC;;;ACHM,SAASC,wBAAuB,YAAY,WAAW;AAC5D,SAAO,aAAa,aAAa,WAAW,SAAS,IAAI;AAC3D;;;ACAO,SAASC,0BAAyB,YAAY,UAAU;AAC7D,SAAOC,wBAAuB,YAAY,SAAS,YAAY,CAAC;AAClE;;;ACNO,IAAMC,SAAQC,QAAO;AAAA,EAC1B,OAAO;AAAA,EACP,YAAY,EAAC,YAAY,cAAa;AAAA,EACtC,WAAWC;AAAA,EACX,YAAY,EAAC,OAAO,MAAM,YAAY,KAAI;AAC5C,CAAC;;;ACLM,IAAMC,QAAOC,QAAO;AAAA,EACzB,UAAU,GAAG,MAAM;AACjB,WAAO,SAAS,SAAS,OAAO,UAAU,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,EACtE;AAAA,EACA,YAAY;AAAA,IACV,sBAAsB;AAAA,IACtB,YAAYC;AAAA,IACZ,kBAAkB;AAAA,IAClB,UAAUA;AAAA,IACV,aAAaA;AAAA,IACb,cAAcC;AAAA,IACd,cAAcA;AAAA,IACd,aAAaA;AAAA,IACb,cAAcC;AAAA,IACd,aAAa;AAAA,IACb,iBAAiBA;AAAA,IACjB,aAAa;AAAA,IACb,cAAcF;AAAA,IACd,gBAAgBE;AAAA,IAChB,kBAAkB;AAAA,IAClB,cAAcF;AAAA,IACd,YAAYE;AAAA,IACZ,aAAaF;AAAA,IACb,cAAc;AAAA,IACd,YAAYA;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,gBAAgBE;AAAA,IAChB,WAAWD;AAAA,IACX,UAAU;AAAA,IACV,WAAWD;AAAA,IACX,eAAeA;AAAA,IACf,qBAAqBA;AAAA,IACrB,iBAAiB;AAAA,IACjB,UAAUE;AAAA,IACV,iBAAiB;AAAA,IACjB,cAAcD;AAAA,IACd,aAAaD;AAAA,IACb,cAAcA;AAAA,IACd,cAAc;AAAA,IACd,cAAcA;AAAA,IACd,qBAAqBE;AAAA,IACrB,cAAcD;AAAA,IACd,cAAcA;AAAA,IACd,aAAaA;AAAA,IACb,cAAcD;AAAA,IACd,aAAaC;AAAA,IACb,UAAU;AAAA,IACV,cAAcA;AAAA,IACd,cAAcA;AAAA,IACd,cAAcA;AAAA,IACd,eAAe;AAAA,IACf,MAAM;AAAA,EACR;AACF,CAAC;;;AC/CM,IAAME,QAAOC,QAAO;AAAA,EACzB,OAAO;AAAA,EACP,YAAY;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,WAAWC;AAAA,EACX,iBAAiB,CAAC,WAAW,YAAY,SAAS,UAAU;AAAA,EAC5D,YAAY;AAAA;AAAA,IAEV,MAAM;AAAA,IACN,QAAQC;AAAA,IACR,eAAeC;AAAA,IACf,WAAWA;AAAA,IACX,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,iBAAiBC;AAAA,IACjB,qBAAqBA;AAAA,IACrB,gBAAgBA;AAAA,IAChB,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,OAAOA;AAAA,IACP,gBAAgB;AAAA,IAChB,cAAcD;AAAA,IACd,WAAWC;AAAA,IACX,UAAUA;AAAA,IACV,UAAUD;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAASC;AAAA,IACT,MAAM;AAAA,IACN,WAAWD;AAAA,IACX,MAAME;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,iBAAiBC;AAAA,IACjB,UAAUF;AAAA,IACV,cAAcD;AAAA,IACd,QAAQE,UAASH;AAAA,IACjB,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAASE;AAAA,IACT,OAAOA;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,UAAUA;AAAA,IACV,UAAUG;AAAA,IACV,WAAWD;AAAA,IACX,SAAS;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,gBAAgBF;AAAA,IAChB,YAAY;AAAA,IACZ,SAASD;AAAA,IACT,QAAQE;AAAA,IACR,QAAQD;AAAA,IACR,MAAMC;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAASF;AAAA,IACT,WAAWA;AAAA,IACX,IAAI;AAAA,IACJ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,OAAOC;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,OAAOA;AAAA,IACP,QAAQ;AAAA,IACR,UAAUD;AAAA,IACV,SAASA;AAAA,IACT,WAAWC;AAAA,IACX,UAAUD;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAMC;AAAA,IACN,KAAKC;AAAA,IACL,UAAU;AAAA,IACV,KAAK;AAAA,IACL,WAAWA;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,WAAWA;AAAA,IACX,UAAUD;AAAA,IACV,OAAOA;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAUA;AAAA,IACV,YAAYA;AAAA,IACZ,SAAS;AAAA,IACT,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,2BAA2B;AAAA,IAC3B,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,sBAAsB;AAAA,IACtB,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,MAAMA;AAAA,IACN,SAASC;AAAA,IACT,SAAS;AAAA,IACT,MAAMF;AAAA,IACN,aAAa;AAAA,IACb,aAAaC;AAAA,IACb,SAAS;AAAA,IACT,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAUA;AAAA,IACV,gBAAgB;AAAA,IAChB,KAAKD;AAAA,IACL,UAAUC;AAAA,IACV,UAAUA;AAAA,IACV,MAAMC;AAAA,IACN,SAASA;AAAA,IACT,SAASF;AAAA,IACT,OAAO;AAAA,IACP,QAAQC;AAAA,IACR,UAAUA;AAAA,IACV,UAAUA;AAAA,IACV,oBAAoBA;AAAA,IACpB,0BAA0BA;AAAA,IAC1B,gBAAgB;AAAA,IAChB,OAAO;AAAA,IACP,MAAMC;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAMA;AAAA,IACN,YAAYC;AAAA,IACZ,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAOD;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAUA;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,IACN,eAAeD;AAAA,IACf,QAAQ;AAAA,IACR,OAAOE;AAAA,IACP,OAAOD;AAAA,IACP,MAAM;AAAA,IACN,oBAAoB;AAAA;AAAA;AAAA,IAIpB,OAAO;AAAA;AAAA,IACP,OAAO;AAAA;AAAA,IACP,SAASF;AAAA;AAAA,IACT,MAAM;AAAA;AAAA,IACN,YAAY;AAAA;AAAA,IACZ,SAAS;AAAA;AAAA,IACT,QAAQE;AAAA;AAAA,IACR,aAAa;AAAA;AAAA,IACb,cAAcA;AAAA;AAAA,IACd,aAAa;AAAA;AAAA,IACb,aAAa;AAAA;AAAA,IACb,MAAM;AAAA;AAAA,IACN,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,OAAO;AAAA;AAAA,IACP,SAASD;AAAA;AAAA,IACT,SAASA;AAAA;AAAA,IACT,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,OAAO;AAAA;AAAA,IACP,aAAa;AAAA;AAAA,IACb,QAAQC;AAAA;AAAA,IACR,YAAYA;AAAA;AAAA,IACZ,MAAM;AAAA;AAAA,IACN,UAAU;AAAA;AAAA,IACV,QAAQ;AAAA;AAAA,IACR,cAAcA;AAAA;AAAA,IACd,aAAaA;AAAA;AAAA,IACb,UAAUD;AAAA;AAAA,IACV,QAAQA;AAAA;AAAA,IACR,SAASA;AAAA;AAAA,IACT,QAAQA;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,KAAK;AAAA;AAAA,IACL,aAAaC;AAAA;AAAA,IACb,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,WAAWC;AAAA;AAAA,IACX,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,MAAM;AAAA;AAAA,IACN,WAAWD;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,IACX,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,QAAQA;AAAA;AAAA;AAAA,IAGR,mBAAmB;AAAA,IACnB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,yBAAyBD;AAAA,IACzB,uBAAuBA;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAASC;AAAA,IACT,UAAU;AAAA,IACV,cAAc;AAAA,EAChB;AACF,CAAC;;;ACvTM,IAAMG,OAAMC,QAAO;AAAA,EACxB,OAAO;AAAA,EACP,YAAY;AAAA,IACV,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,UAAU;AAAA,IACV,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA;AAAA,IAET,eAAe;AAAA,IACf,eAAe;AAAA,EACjB;AAAA,EACA,WAAWC;AAAA,EACX,YAAY;AAAA,IACV,OAAOC;AAAA,IACP,cAAcC;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,YAAYA;AAAA,IACZ,WAAWA;AAAA,IACX,YAAY;AAAA,IACZ,QAAQA;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,SAASA;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,eAAe;AAAA,IACf,aAAa;AAAA,IACb,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAMA;AAAA,IACN,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,WAAWA;AAAA,IACX,WAAWC;AAAA,IACX,MAAM;AAAA,IACN,UAAU;AAAA,IACV,eAAe;AAAA,IACf,UAAU;AAAA,IACV,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,UAAU;AAAA,IACV,eAAe;AAAA,IACf,SAASD;AAAA,IACT,iBAAiBA;AAAA,IACjB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,KAAK;AAAA,IACL,SAASA;AAAA,IACT,kBAAkB;AAAA,IAClB,UAAUE;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAWF;AAAA,IACX,kBAAkB;AAAA,IAClB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,UAAUA;AAAA,IACV,2BAA2B;AAAA,IAC3B,MAAM;AAAA,IACN,aAAaA;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAIG;AAAA,IACJ,IAAIA;AAAA,IACJ,WAAWA;AAAA,IACX,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,SAASH;AAAA,IACT,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAWA;AAAA,IACX,cAAcA;AAAA,IACd,cAAcA;AAAA,IACd,IAAI;AAAA,IACJ,aAAaA;AAAA,IACb,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,WAAWA;AAAA,IACX,GAAGA;AAAA,IACH,IAAIA;AAAA,IACJ,IAAIA;AAAA,IACJ,IAAIA;AAAA,IACJ,IAAIA;AAAA,IACJ,cAAcD;AAAA,IACd,kBAAkB;AAAA,IAClB,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,UAAU;AAAA;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,mBAAmBC;AAAA,IACnB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,aAAa;AAAA,IACb,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,KAAK;AAAA,IACL,OAAO;AAAA,IACP,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,WAAWA;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,kBAAkBA;AAAA,IAClB,mBAAmBA;AAAA,IACnB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,YAAYA;AAAA,IACZ,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,OAAO;AAAA,IACP,MAAMC;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,WAAWD;AAAA,IACX,WAAWA;AAAA,IACX,WAAWA;AAAA,IACX,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAUD;AAAA,IACV,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAKA;AAAA,IACL,KAAKA;AAAA,IACL,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,oBAAoBA;AAAA,IACpB,kBAAkBA;AAAA,IAClB,eAAeA;AAAA,IACf,iBAAiBA;AAAA,IACjB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,kBAAkBC;AAAA,IAClB,kBAAkBA;AAAA,IAClB,cAAc;AAAA,IACd,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,OAAO;AAAA,IACP,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,uBAAuBA;AAAA,IACvB,wBAAwBA;AAAA,IACxB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,iBAAiBD;AAAA,IACjB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkBC;AAAA,IAClB,eAAeA;AAAA,IACf,aAAa;AAAA,IACb,OAAO;AAAA,IACP,cAAcA;AAAA,IACd,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,sBAAsB;AAAA,IACtB,gBAAgBD;AAAA,IAChB,UAAUC;AAAA,IACV,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAASA;AAAA,IACT,SAASA;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,OAAO;AAAA,IACP,mBAAmB;AAAA,IACnB,MAAM;AAAA,IACN,QAAQD;AAAA,IACR,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,mBAAmBC;AAAA,IACnB,oBAAoBA;AAAA,IACpB,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAYA;AAAA,IACZ,QAAQ;AAAA,IACR,aAAaA;AAAA,IACb,eAAeA;AAAA,IACf,cAAc;AAAA,IACd,UAAUA;AAAA,IACV,cAAcA;AAAA,IACd,SAAS;AAAA,IACT,UAAUA;AAAA,IACV,aAAaA;AAAA,IACb,aAAaA;AAAA,IACb,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,SAASA;AAAA,IACT,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,GAAG;AAAA,IACH,YAAY;AAAA,EACd;AACF,CAAC;;;AC9iBD,IAAMI,SAAQ;AACd,IAAMC,QAAO;AACb,IAAMC,OAAM;AAOL,SAASC,MAAK,QAAQ,OAAO;AAClC,QAAM,SAASC,WAAU,KAAK;AAC9B,MAAI,OAAO;AACX,MAAI,OAAOC;AAEX,MAAI,UAAU,OAAO,QAAQ;AAC3B,WAAO,OAAO,SAAS,OAAO,OAAO,MAAM,CAAC;AAAA,EAC9C;AAEA,MAAI,OAAO,SAAS,KAAK,OAAO,MAAM,GAAG,CAAC,MAAM,UAAUL,OAAM,KAAK,KAAK,GAAG;AAE3E,QAAI,MAAM,OAAO,CAAC,MAAM,KAAK;AAE3B,YAAM,OAAO,MAAM,MAAM,CAAC,EAAE,QAAQC,OAAMK,UAAS;AACnD,aAAO,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,IAC7D,OAAO;AAEL,YAAM,OAAO,MAAM,MAAM,CAAC;AAE1B,UAAI,CAACL,MAAK,KAAK,IAAI,GAAG;AACpB,YAAI,SAAS,KAAK,QAAQC,MAAKK,MAAK;AAEpC,YAAI,OAAO,OAAO,CAAC,MAAM,KAAK;AAC5B,mBAAS,MAAM;AAAA,QACjB;AAEA,gBAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAEA,WAAOC;AAAA,EACT;AAEA,SAAO,IAAI,KAAK,MAAM,KAAK;AAC7B;AAMA,SAASD,OAAM,IAAI;AACjB,SAAO,MAAM,GAAG,YAAY;AAC9B;AAMA,SAASD,WAAU,IAAI;AACrB,SAAO,GAAG,OAAO,CAAC,EAAE,YAAY;AAClC;;;ACnDO,IAAMG,QAAOC,OAAM,CAACC,MAAKC,QAAOC,QAAOC,OAAML,KAAQ,GAAG,MAAM;AAC9D,IAAMM,OAAML,OAAM,CAACC,MAAKC,QAAOC,QAAOC,OAAMC,IAAO,GAAG,KAAK;;;AC4BlE,IAAM,eAAe,CAAC;AAEtB,IAAMC,OAAM,CAAC,EAAE;AAEf,IAAMC,OAAM,OAAO,QAAQ,EAAC,UAAU,EAAC,MAAM,SAAAC,UAAS,MAAM,SAAS,QAAO,EAAC,CAAC;AAYvE,SAAS,SAAS,MAAM,SAAS;AACtC,QAAM,WAAW,WAAW;AAC5B,QAAM,QAAQ,SAAS;AACvB,SAAOD,KAAI,MAAM,UAAU,QAAQE,OAAMC,KAAI;AAC/C;AAUA,SAAS,KAAK,MAAM,QAAQ;AAE1B,QAAM,SAAS;AAAA,IACb,UAAU;AAAA;AAAA,IAEV,OAAO,KAAK,QAAQ,CAAC,GAAG,aAAa,WAAW;AAAA,IAChD,YAAY,CAAC;AAAA,EACf;AACA,SAAO,aAAaC,KAAI,KAAK,UAAU,QAAQ,MAAM;AACrD,EAAAC,OAAM,MAAM,MAAM;AAClB,SAAO;AACT;AAUA,SAAS,SAAS,MAAM,QAAQ;AAE9B,QAAM,SAAS,EAAC,UAAU,sBAAsB,YAAY,CAAC,EAAC;AAC9D,SAAO,aAAaD,KAAI,KAAK,UAAU,QAAQ,MAAM;AACrD,EAAAC,OAAM,MAAM,MAAM;AAClB,SAAO;AACT;AAQA,SAAS,QAAQ,MAAM;AAErB,QAAM,SAAS;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAEA,EAAAA,OAAM,MAAM,MAAM;AAClB,SAAO;AACT;AAQA,SAAS,KAAK,MAAM;AAElB,QAAM,SAAS;AAAA,IACb,UAAU;AAAA,IACV,OAAO,KAAK;AAAA,IACZ,YAAY;AAAA,EACd;AACA,EAAAA,OAAM,MAAM,MAAM;AAClB,SAAO;AACT;AAQA,SAAS,QAAQ,MAAM;AAErB,QAAM,SAAS;AAAA,IACb,UAAU;AAAA,IACV,MAAM,KAAK;AAAA,IACX,YAAY;AAAA,EACd;AAEA,EAAAA,OAAM,MAAM,MAAM;AAElB,SAAO;AACT;AAUA,SAASJ,SAAQ,MAAM,QAAQ;AAC7B,QAAM,eAAe;AACrB,MAAI,gBAAgB;AAEpB,MACE,KAAK,SAAS,aACd,KAAK,QAAQ,YAAY,MAAM,SAC/B,aAAa,UAAU,QACvB;AACA,oBAAgBC;AAAA,EAClB;AAGA,QAAM,QAAQ,CAAC;AAEf,MAAI;AAEJ,MAAI,KAAK,YAAY;AACnB,SAAK,QAAQ,KAAK,YAAY;AAC5B,UAAI,SAAS,cAAcH,KAAI,KAAK,KAAK,YAAY,IAAI,GAAG;AAC1D,cAAMO,UAAS;AAAA,UACb;AAAA,UACA;AAAA,UACA,KAAK,WAAW,IAAI;AAAA,QACtB;AAEA,YAAIA,SAAQ;AACV,gBAAM,KAAKA,OAAM;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,QAAQ,cAAc;AAE5B,KAAO,KAAK;AAGZ,QAAM,SAAS;AAAA,IACb,UAAU,KAAK;AAAA,IACf,SAAS,KAAK;AAAA,IACd;AAAA;AAAA,IAEA,cAAc,cAAc,KAAK;AAAA,IACjC,YAAY,CAAC;AAAA,IACb,YAAY;AAAA,EACd;AACA,SAAO,aAAaF,KAAI,KAAK,UAAU,QAAQ,aAAa;AAC5D,EAAAC,OAAM,MAAM,MAAM;AAElB,MAAI,KAAK,YAAY,cAAc,KAAK,SAAS;AAE/C,WAAO,UAAU,SAAS,KAAK,SAAS,aAAa;AAAA,EACvD;AAEA,SAAO;AACT;AAcA,SAAS,eAAe,QAAQ,MAAM,OAAO;AAC3C,QAAM,OAAOE,MAAK,QAAQ,IAAI;AAG9B,MACE,UAAU,SACV,UAAU,QACV,UAAU,UACT,OAAO,UAAU,YAAY,OAAO,MAAM,KAAK,KAC/C,CAAC,SAAS,KAAK,SAChB;AACA;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AAGxB,YAAQ,KAAK,iBAAiB,UAAO,KAAK,IAAIC,WAAO,KAAK;AAAA,EAC5D;AAGA,QAAM,YAAY;AAAA,IAChB,MAAM,KAAK;AAAA,IACX,OAAO,UAAU,OAAO,KAAK,OAAO,KAAK;AAAA,EAC3C;AAEA,MAAI,KAAK,SAAS,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO;AAC/D,UAAM,QAAQ,UAAU,KAAK,QAAQ,GAAG;AAExC,QAAI,QAAQ,GAAG;AACb,gBAAU,SAAS;AAAA,IACrB,OAAO;AACL,gBAAU,OAAO,UAAU,KAAK,MAAM,QAAQ,CAAC;AAC/C,gBAAU,SAAS,KAAK,UAAU,MAAM,GAAG,KAAK;AAAA,IAClD;AAEA,cAAU,YAAY,cAAc,KAAK,KAAK;AAAA,EAChD;AAEA,SAAO;AACT;AAcA,SAASJ,KAAI,UAAU,YAAY,QAAQ;AACzC,MAAI,QAAQ;AAEZ,QAAM,UAAU,CAAC;AAEjB,MAAI,UAAU;AACZ,WAAO,EAAE,QAAQ,SAAS,QAAQ;AAEhC,YAAM,QAAQJ,KAAI,SAAS,KAAK,GAAG,MAAM;AAEzC,YAAM,aAAa;AAEnB,cAAQ,KAAK,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAASK,OAAM,MAAM,IAAI;AACvB,QAAMI,YAAW,KAAK;AAEtB,MAAIA,aAAYA,UAAS,SAASA,UAAS,KAAK;AAC9C,OAAO,OAAOA,UAAS,MAAM,WAAW,QAAQ;AAChD,OAAO,OAAOA,UAAS,IAAI,WAAW,QAAQ;AAE9C,OAAG,qBAAqB;AAAA,MACtB,WAAWA,UAAS,MAAM;AAAA,MAC1B,UAAUA,UAAS,MAAM;AAAA,MACzB,aAAaA,UAAS,MAAM;AAAA,MAC5B,SAASA,UAAS,IAAI;AAAA,MACtB,QAAQA,UAAS,IAAI;AAAA,MACrB,WAAWA,UAAS,IAAI;AAAA,IAC1B;AAAA,EACF;AACF;;;AC5UO,IAAM,mBAAmB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACeA,IAAM,yBACJ;AAIF,IAAM,gBAAgB,oBAAI,IAAI;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAGD,IAAM,eAAe,EAAC,wBAAwB,MAAM,kBAAkB,MAAK;AAapE,SAAS,IAAI,MAAM,SAAS;AACjC,QAAM,WAAW,aAAa,IAAI;AAElC,QAAMC,OAAM,OAAO,QAAQ;AAAA,IACzB,UAAU,EAAC,MAAAC,OAAM,SAAAC,UAAS,MAAAC,OAAM,SAAAC,UAAS,SAAAC,UAAS,KAAK,UAAS;AAAA,IAChE;AAAA,EACF,CAAC;AAGD,QAAM,QAAQ;AAAA,IACZ,QAAQ,WACJ,IAAI,OAAO,YAAY,IACvB,OAAO,kBAAkB,QAAW,YAAY;AAAA,IACpD,OAAO,MAAM;AACX,MAAAL,KAAI,MAAM,KAAK;AAAA,IACjB;AAAA,IACA,UAAU;AAAA,IACV,SAAS,WAAW,CAAC;AAAA,EACvB;AAEA,EAAAA,KAAI,MAAM,KAAK;AACf,iBAAe,OAAO,WAAW,CAAC;AAElC,QAAM,KAAK,WAAW,MAAM,OAAO,WAAW,MAAM,OAAO,YAAY;AACvE,QAAM,SAAS,WAAW,IAAI;AAAA;AAAA,IAE5B,MAAM,MAAM,QAAQ;AAAA,EACtB,CAAC;AAED,MAAI,MAAM,UAAU;AAClB,UAAM,QAAQ,WAAW,SAAU,MAAM,OAAO,QAAQ;AACtD,YAAMM;AAAA;AAAA;AAAA,QAAwD;AAAA;AAC9D,UAAIA,QAAO,MAAM,UAAU,UAAU,UAAU,QAAW;AAExD,cAAM,WAAW,OAAO;AAExB,iBAAS,KAAK,IAAIA,QAAO,MAAM;AAC/B,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAGA,MACE,OAAO,SAAS,UAChB,OAAO,SAAS,WAAW,KAC3B,OAAO,SAAS,CAAC,EAAE,SAAS,KAAK,MACjC;AACA,WAAO,OAAO,SAAS,CAAC;AAAA,EAC1B;AAEA,SAAO;AACT;AAYA,SAASC,KAAI,OAAO,OAAO;AACzB,MAAI,QAAQ;AAGZ,MAAI,OAAO;AACT,WAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,YAAM,OAAO,MAAM,KAAK,CAAC;AAAA,IAC3B;AAAA,EACF;AACF;AAYA,SAASN,MAAK,MAAM,OAAO;AACzB,EAAAM,KAAI,KAAK,UAAU,KAAK;AAC1B;AAYA,SAASL,SAAQ,MAAM,OAAO;AAC5B,WAAS,MAAM,KAAK;AAEpB,EAAAK,KAAI,KAAK,UAAU,KAAK;AAExB,SAAO,MAAM,KAAK;AACpB;AAYA,SAASJ,MAAK,MAAM,OAAO;AAMzB,MAAI,MAAM,OAAO,UAAU,QAAQ,GAAG;AACpC,UAAM,OAAO,UAAU,QAAQ;AAAA,EACjC;AAGA,QAAM,QAAQ;AAAA,IACZ,MAAM,cAAM,UAAU;AAAA,IACtB,OAAO,KAAK;AAAA,IACZ,UAAU,qBAAqB,IAAI;AAAA,EACrC;AAEA,iBAAe,OAAO,WAAW,IAAI,CAAC;AAEtC,QAAM,OAAO,eAAe;AAE5B,QAAM,OAAO,cAAc,MAAM,OAAO,YAAY;AACtD;AAYA,SAASE,SAAQ,MAAM,OAAO;AAE5B,QAAM,QAAQ;AAAA,IACZ,MAAM,cAAM,UAAU;AAAA,IACtB,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU,qBAAqB,IAAI;AAAA,EACrC;AAEA,iBAAe,OAAO,WAAW,IAAI,CAAC;AAEtC,QAAM,OAAO,eAAe;AAE5B,QAAM,OAAO,cAAc,MAAM,OAAO,YAAY;AACtD;AAYA,SAAS,OAAO,MAAM,OAAO;AAE3B,QAAM,WAAW;AAGjB,QAAM,QAAQ,qBAAqB,IAAI;AAIvC,MAAI,cAAc,QAAQ,cAAc,OAAO;AAE7C,UAAM;AAAA;AAAA,MACJ,IAAI,EAAC,MAAM,QAAQ,UAAU,KAAK,SAAQ,GAAG,MAAM,OAAO;AAAA;AAE5D,UAAM,WAAW,SAAS;AAAA,EAC5B;AAKA,EAAAD,SAAQ,EAAC,MAAM,WAAW,OAAO,EAAC,QAAQ,MAAK,EAAC,GAAG,KAAK;AAC1D;AAYA,SAASA,SAAQ,MAAM,OAAO;AAG5B,QAAM,OAAO,KAAK;AAGlB,QAAM,QAAQ;AAAA,IACZ,MAAM,cAAM,UAAU;AAAA,IACtB;AAAA,IACA,UAAU,qBAAqB,IAAI;AAAA,EACrC;AACA,iBAAe,OAAO,WAAW,IAAI,CAAC;AAEtC,QAAM,OAAO,eAAe;AAE5B,QAAM,OAAO,cAAc,MAAM,OAAO,YAAY;AACtD;AAYA,SAAS,UAAU,MAAM,OAAO;AAG9B,QAAM,OAAO,UAAU,aAAa,OAAO;AAC3C,QAAM,OAAO,UAAU,aAAa,MAAM;AAG1C,QAAM,OAAO,UAAU,aAAa,aAAa;AAGjD,QAAM,OAAO,UAAU,aAAa,WAAW,CAAC;AAGhD,QAAM,OAAO,UAAU,aAAa,kBAAkB;AACtD,QAAM,OAAO,UAAU,aAAa,mBAAmB;AACvD,QAAM,OAAO,UAAU,aAAa,gBAAgB;AAGpD,QAAM,OAAO,UAAU,aAAa,QAAQ;AAG5C,WAAS,OAAO,WAAW,IAAI,CAAC;AAEhC,QAAM,OAAO,UAAU;AAAA,IACrB,MAAM,QAAQ,YACV,KAAK,MAAM,QAAQ,wBAAwB,UAAU,IACrD,KAAK;AAAA,IACT;AAAA,EACF;AAEA,QAAM,OAAO,UAAU,gBAAgB;AAcvC,MACE,MAAM,OAAO,UAAU,UAAU;AAAA,EAEjC,MAAM,OAAO,UAAU,UAAU,IACjC;AACA,UAAM,OAAO,UAAU,aAAa,mBAAmB;AAGvD,UAAM,KAAK,MAAM,OAAO,UAAU,SAAS;AAE3C,UAAM,OAAO,UAAU,WAAW,EAAE;AAAA,EACtC;AACF;AAYA,SAAS,QAAQ,OAAO,OAAO;AAC7B,QAAM;AAAA;AAAA,IAA6B;AAAA;AAEnC,MACE,MAAM,QAAQ,eACd,MAAM,QAAQ,YAAY,SAAS,KAAK,IAAI,GAC5C;AACA,WAAO,MAAM,KAAK;AAAA,EACpB,OAAO;AACL,QAAI,QAAQ;AAEZ,QAAI,cAAc,IAAI,KAAK,IAAI,GAAG;AAChC,cACE;AAAA,IACJ;AAEA,UAAM,IAAI,MAAM,qBAAqB,KAAK,OAAO,WAAW,KAAK;AAAA,EACnE;AACF;AAYA,SAAS,eAAe,OAAOI,QAAO;AACpC,WAAS,OAAOA,MAAK;AAKrB,QAAM,QAAQ,MAAM,OAAO,UAAU;AAErC,MAAI,SAAS,MAAM,UAAU;AAC3B,UAAM,SAAS,UAAU,MAAM,OAAO,UAAU,aAAa;AAC7D,UAAM,SAAS,SAAS,MAAM,OAAO,UAAU,aAAa,MAAM;AAClE,UAAM,SAAS,YAAY,MAAM,OAAO,UAAU,aAAa,SAAS;AAExE,UAAM,OAAO,eAAe;AAE5B,UAAM,OAAO,cAAc,MAAM,OAAO,YAAY;AAAA,EACtD;AAUA,QAAM,OAAO,UAAU,SAAS;AAEhC,QAAM,OAAO,UAAU,SAAS;AAIhC,QAAM,OAAO,UAAU,SAAS;AAEhC,QAAM,OAAO,UAAU,cAAc,cAAc;AAEnD,QAAM,OAAO,UAAU,cAAc;AAErC,QAAM,OAAO,UAAU,wBAAwB;AAE/C,QAAM,OAAO,UAAU,kBAAkB;AAEzC,QAAM,OAAO,UAAU,wBAAwB;AAE/C,QAAM,OAAO,UAAU,eAAe;AAEtC,QAAM,OAAO,UAAU,cAAc,EAAC,MAAM,IAAI,OAAO,GAAE;AAC3D;AAYA,SAAS,SAAS,OAAOA,QAAO;AAC9B,MAAIA,UAASA,OAAM,WAAW,QAAW;AAEvC,UAAMC,YAAW;AAAA,MACf,WAAWD,OAAM;AAAA,MACjB,UAAUA,OAAM;AAAA,MAChB,aAAaA,OAAM;AAAA,MACnB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAIA,UAAM,OAAO,UAAU,aAAa,eAAe,CAACA,OAAM,SAAS;AACnE,UAAM,OAAO,UAAU,aAAa,oBAAoBA,OAAM;AAC9D,UAAM,OAAO,UAAU,aAAa,OAAOA,OAAM;AAEjD,UAAM,OAAO,UAAU,kBAAkBC;AAAA,EAC3C;AACF;AAYA,SAAS,SAAS,MAAM,OAAO;AAC7B,QAAM,UAAU,KAAK,QAAQ,YAAY;AAGzC,MAAI,MAAM,OAAO,UAAU,UAAU,cAAc,UAAW;AAE9D,iBAAe,OAAO,WAAW,IAAI,CAAC;AAEtC,QAAM,UAAU,MAAM,OAAO,aAAa;AAC1C,MAAI,KAAK,kBAAkB,UAAU,QAAQ,eAAe,cAAc;AAE1E,MAAI,OAAO,cAAc,QAAQ,YAAY,OAAO;AAClD,SAAK,cAAc;AAAA,EACrB;AAEA,QAAM,SAAS;AAAA;AAAA,IAEb,EAAC,GAAG,MAAM,UAAU,CAAC,EAAC;AAAA,IACtB,EAAC,OAAO,OAAO,cAAc,MAAM,QAAQ,OAAM;AAAA,EACnD;AAGA,QAAM,MAAM;AAAA,IACV,MAAM,cAAM,UAAU;AAAA,IACtB;AAAA,IACA,OAAO,aAAK,SAAS,OAAO;AAAA;AAAA,IAE5B,aAAa;AAAA,IACb,gBAAgB;AAAA;AAAA;AAAA,IAGhB,OAAO,WAAW,SAAS,OAAO,QAAQ,CAAC;AAAA,IAC3C,UAAU,qBAAqB,IAAI;AAAA,EACrC;AAQA,QAAM,OAAO,eAAe;AAE5B,QAAM,OAAO,cAAc,MAAM,OAAO,YAAY;AAMpD,QAAM,OAAO,UAAU,mBAAmB;AAG5C;AAYA,SAAS,OAAO,MAAM,OAAO;AAC3B,QAAM,UAAU,KAAK,QAAQ,YAAY;AAEzC,MACE,CAAC,MAAM,OAAO,UAAU,iBACxB,iBAAiB,SAAS,OAAO,GACjC;AACA;AAAA,EACF;AAGA,MAAI,MAAM,OAAO,UAAU,UAAU,cAAc,UAAW;AAE9D,iBAAe,OAAO,SAAS,IAAI,CAAC;AAGpC,QAAM,MAAM;AAAA,IACV,MAAM,cAAM,UAAU;AAAA,IACtB;AAAA,IACA,OAAO,aAAK,SAAS,OAAO;AAAA,IAC5B,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,OAAO,CAAC;AAAA,IACR,UAAU,qBAAqB,IAAI;AAAA,EACrC;AAQA,QAAM,OAAO,eAAe;AAE5B,QAAM,OAAO,cAAc,MAAM,OAAO,YAAY;AAOpD;AAAA;AAAA,IAEE,YAAY,MAAM,OAAO,UAAU;AAAA,KAElC,MAAM,OAAO,UAAU,UAAU,cAAc;AAAA,IAE9C,MAAM,OAAO,UAAU,UAAU,cAAc;AAAA,IAE/C,MAAM,OAAO,UAAU,UAAU,cAAc;AAAA,IAEjD;AACA,UAAM,OAAO,UAAU,QAAQ,cAAc;AAAA,EAC/C;AACF;AAUA,SAAS,aAAa,MAAM;AAC1B,QAAM,OAAO,KAAK,SAAS,SAAS,KAAK,SAAS,CAAC,IAAI;AACvD,SAAO;AAAA,IACL,SACG,KAAK,SAAS,aACZ,KAAK,SAAS,aAAa,KAAK,QAAQ,YAAY,MAAM;AAAA,EACjE;AACF;AAUA,SAAS,qBAAqB,MAAM;AAClC,QAAM,QAAQ,WAAW,IAAI,KAAK;AAAA,IAChC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AACA,QAAM,MAAM,SAAS,IAAI,KAAK;AAAA,IAC5B,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAGA,QAAMA,YAAW;AAAA,IACf,WAAW,MAAM;AAAA,IACjB,UAAU,MAAM;AAAA,IAChB,aAAa,MAAM;AAAA,IACnB,SAAS,IAAI;AAAA,IACb,QAAQ,IAAI;AAAA,IACZ,WAAW,IAAI;AAAA,EACjB;AAIA,SAAOA;AACT;AAUA,SAAS,qBAAqB,MAAM;AAClC,SAAO,cAAc,OACjB,YAAgB,EAAC,GAAG,MAAM,UAAU,CAAC,EAAC,CAAC,IACvC,YAAgB,IAAI;AAC1B;;;ACppBe,SAAR,UAA2B,SAAS;AASzC,SAAO,SAAU,MAAM,MAAM;AAE3B,UAAM;AAAA;AAAA,MAA8B,IAAI,MAAM,EAAC,GAAG,SAAS,KAAI,CAAC;AAAA;AAChE,WAAO;AAAA,EACT;AACF;", "names": ["html", "svg", "parse", "stringify", "h", "value", "parse", "html", "svg", "point", "svg", "html", "startTag", "endTag", "position", "location", "point", "<PERSON><PERSON><PERSON>", "merge", "<PERSON><PERSON><PERSON>", "normalize", "Info", "types_exports", "boolean", "booleanish", "commaOrSpaceSeparated", "commaSeparated", "number", "overloadedBoolean", "spaceSeparated", "powers", "increment", "checks", "types_exports", "DefinedInfo", "Info", "mark", "own", "create", "DefinedInfo", "normalize", "<PERSON><PERSON><PERSON>", "xlink", "create", "xml", "create", "caseSensitiveTransform", "caseInsensitiveTransform", "caseSensitiveTransform", "xmlns", "create", "caseInsensitiveTransform", "aria", "create", "booleanish", "number", "spaceSeparated", "html", "create", "caseInsensitiveTransform", "commaSeparated", "spaceSeparated", "boolean", "number", "booleanish", "overloadedBoolean", "svg", "create", "caseSensitiveTransform", "commaOrSpaceSeparated", "number", "spaceSeparated", "boolean", "commaSeparated", "valid", "dash", "cap", "find", "normalize", "Info", "camelcase", "kebab", "DefinedInfo", "html", "merge", "xml", "xlink", "xmlns", "aria", "svg", "own", "one", "element", "svg", "html", "all", "patch", "result", "find", "stringify", "position", "one", "root", "element", "text", "comment", "doctype", "stitch", "all", "point", "location"]}