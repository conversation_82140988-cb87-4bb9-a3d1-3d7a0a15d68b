{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "~": [".."], "~/*": ["../*"], "@": [".."], "@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#app": ["../node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/nuxt/dist/app/compat/vue-demi"], "#content/components": ["./content/components"], "#content/manifest": ["./content/manifest"], "#mdc-configs": ["./mdc-configs"], "#mdc-highlighter": ["./mdc-highlighter"], "#mdc-imports": ["./mdc-imports"], "#image": ["../../node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../../node_modules/@nuxt/image/dist/runtime/*"], "#ui": ["../../node_modules/@nuxt/ui/dist/runtime"], "#ui/*": ["../../node_modules/@nuxt/ui/dist/runtime/*"], "#color-mode-options": ["./color-mode-options.mjs"], "#vue-router": ["../node_modules/vue-router"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables/v3"], "#imports": ["./imports"], "#tailwindcss": ["./tailwind"], "#tailwindcss/sources": ["./tailwindcss/sources"], "vue-i18n": ["../../node_modules/vue-i18n/dist/vue-i18n"], "@intlify/shared": ["../../node_modules/@intlify/shared/dist/shared"], "@intlify/message-compiler": ["../../node_modules/@intlify/message-compiler/dist/message-compiler"], "@intlify/core-base": ["../../node_modules/@intlify/core-base/dist/core-base"], "@intlify/core": ["../../node_modules/@intlify/core/dist/core.node"], "@intlify/utils/h3": ["../../node_modules/@intlify/utils/dist/h3"], "ufo": ["../node_modules/ufo/dist/index"], "#i18n": ["../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index"], "#internal-i18n-types": ["../../node_modules/@nuxtjs/i18n/dist/types"], "#nuxt-i18n/logger": ["./nuxt-i18n-logger"], "#app-manifest": ["./manifest/meta/dev.json"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": false, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["../**/*", "../.config/nuxt.*", "./nuxt.d.ts", "../node_modules/@nuxtjs/mdc/runtime", "../node_modules/@nuxtjs/mdc/dist/runtime", "../node_modules/@nuxt/content/runtime", "../node_modules/@nuxt/content/dist/runtime", "../../node_modules/@nuxtjs/tailwindcss/runtime", "../../node_modules/@nuxtjs/tailwindcss/dist/runtime", "../../node_modules/@pinia/nuxt/runtime", "../../node_modules/@pinia/nuxt/dist/runtime", "../../node_modules/@nuxtjs/i18n/runtime", "../../node_modules/@nuxtjs/i18n/dist/runtime", "../../node_modules/@nuxt/eslint/runtime", "../../node_modules/@nuxt/eslint/dist/runtime", "../../node_modules/@nuxt/image/runtime", "../../node_modules/@nuxt/image/dist/runtime", "../../node_modules/@nuxt/icon/runtime", "../../node_modules/@nuxt/icon/dist/runtime", "../../node_modules/@nuxt/fonts/runtime", "../../node_modules/@nuxt/fonts/dist/runtime", "../../node_modules/@nuxt/ui/runtime", "../../node_modules/@nuxt/ui/dist/runtime", "../../node_modules/@vueuse/nuxt/runtime", "../../node_modules/@vueuse/nuxt/dist/runtime", "../../node_modules/@nuxtjs/color-mode/runtime", "../../node_modules/@nuxtjs/color-mode/dist/runtime", "../node_modules/@nuxt/devtools/runtime", "../node_modules/@nuxt/devtools/dist/runtime", "../node_modules/@nuxt/telemetry/runtime", "../node_modules/@nuxt/telemetry/dist/runtime", "..", "../../node_modules/vue-i18n/dist/vue-i18n", "../../node_modules/@intlify/shared/dist/shared", "../../node_modules/@intlify/message-compiler/dist/message-compiler", "../../node_modules/@intlify/core-base/dist/core-base", "../../node_modules/@intlify/core/dist/core.node", "../../node_modules/@intlify/utils/dist/h3", "../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index", "../../node_modules/@nuxtjs/i18n/dist/types"], "exclude": ["../dist", "../.data", "../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@nuxtjs/mdc/node_modules", "../node_modules/@nuxt/content/node_modules", "../../node_modules/@nuxtjs/tailwindcss/node_modules", "../../node_modules/@pinia/nuxt/node_modules", "../../node_modules/@nuxtjs/i18n/node_modules", "../../node_modules/@nuxt/eslint/node_modules", "../../node_modules/@nuxt/image/node_modules", "../../node_modules/@nuxt/icon/node_modules", "../../node_modules/@nuxt/fonts/node_modules", "../../node_modules/@nuxt/ui/node_modules", "../../node_modules/@vueuse/nuxt/node_modules", "../../node_modules/@nuxtjs/color-mode/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../node_modules/@nuxtjs/mdc/runtime/server", "../node_modules/@nuxtjs/mdc/dist/runtime/server", "../node_modules/@nuxt/content/runtime/server", "../node_modules/@nuxt/content/dist/runtime/server", "../../node_modules/@nuxtjs/tailwindcss/runtime/server", "../../node_modules/@nuxtjs/tailwindcss/dist/runtime/server", "../../node_modules/@pinia/nuxt/runtime/server", "../../node_modules/@pinia/nuxt/dist/runtime/server", "../../node_modules/@nuxtjs/i18n/runtime/server", "../../node_modules/@nuxtjs/i18n/dist/runtime/server", "../../node_modules/@nuxt/eslint/runtime/server", "../../node_modules/@nuxt/eslint/dist/runtime/server", "../../node_modules/@nuxt/image/runtime/server", "../../node_modules/@nuxt/image/dist/runtime/server", "../../node_modules/@nuxt/icon/runtime/server", "../../node_modules/@nuxt/icon/dist/runtime/server", "../../node_modules/@nuxt/fonts/runtime/server", "../../node_modules/@nuxt/fonts/dist/runtime/server", "../../node_modules/@nuxt/ui/runtime/server", "../../node_modules/@nuxt/ui/dist/runtime/server", "../../node_modules/@vueuse/nuxt/runtime/server", "../../node_modules/@vueuse/nuxt/dist/runtime/server", "../../node_modules/@nuxtjs/color-mode/runtime/server", "../../node_modules/@nuxtjs/color-mode/dist/runtime/server", "../node_modules/@nuxt/devtools/runtime/server", "../node_modules/@nuxt/devtools/dist/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "../node_modules/@nuxt/telemetry/dist/runtime/server", "../.output"]}