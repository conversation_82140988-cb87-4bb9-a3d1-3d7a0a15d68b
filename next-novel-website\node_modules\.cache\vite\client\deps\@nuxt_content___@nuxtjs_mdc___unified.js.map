{"version": 3, "sources": ["../../../../extend/index.js", "../../../../bail/index.js", "../../../../unified/lib/index.js", "../../../../unified/node_modules/is-plain-obj/index.js", "../../../../trough/lib/index.js", "../../../../unist-util-stringify-position/lib/index.js", "../../../../vfile-message/lib/index.js", "../../../../vfile/lib/minpath.browser.js", "../../../../vfile/lib/minproc.browser.js", "../../../../vfile/lib/minurl.shared.js", "../../../../vfile/lib/minurl.browser.js", "../../../../vfile/lib/index.js", "../../../../unified/lib/callable-instance.js"], "sourcesContent": ["'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nvar isArray = function isArray(arr) {\n\tif (typeof Array.isArray === 'function') {\n\t\treturn Array.isArray(arr);\n\t}\n\n\treturn toStr.call(arr) === '[object Array]';\n};\n\nvar isPlainObject = function isPlainObject(obj) {\n\tif (!obj || toStr.call(obj) !== '[object Object]') {\n\t\treturn false;\n\t}\n\n\tvar hasOwnConstructor = hasOwn.call(obj, 'constructor');\n\tvar hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n\t// Not own constructor property must be Object\n\tif (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n\t\treturn false;\n\t}\n\n\t// Own properties are enumerated firstly, so to speed up,\n\t// if last one is own, then all properties are own.\n\tvar key;\n\tfor (key in obj) { /**/ }\n\n\treturn typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n\tif (defineProperty && options.name === '__proto__') {\n\t\tdefineProperty(target, options.name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t\tvalue: options.newValue,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\ttarget[options.name] = options.newValue;\n\t}\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n\tif (name === '__proto__') {\n\t\tif (!hasOwn.call(obj, name)) {\n\t\t\treturn void 0;\n\t\t} else if (gOPD) {\n\t\t\t// In early versions of node, obj['__proto__'] is buggy when obj has\n\t\t\t// __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n\t\t\treturn gOPD(obj, name).value;\n\t\t}\n\t}\n\n\treturn obj[name];\n};\n\nmodule.exports = function extend() {\n\tvar options, name, src, copy, copyIsArray, clone;\n\tvar target = arguments[0];\n\tvar i = 1;\n\tvar length = arguments.length;\n\tvar deep = false;\n\n\t// Handle a deep copy situation\n\tif (typeof target === 'boolean') {\n\t\tdeep = target;\n\t\ttarget = arguments[1] || {};\n\t\t// skip the boolean and the target\n\t\ti = 2;\n\t}\n\tif (target == null || (typeof target !== 'object' && typeof target !== 'function')) {\n\t\ttarget = {};\n\t}\n\n\tfor (; i < length; ++i) {\n\t\toptions = arguments[i];\n\t\t// Only deal with non-null/undefined values\n\t\tif (options != null) {\n\t\t\t// Extend the base object\n\t\t\tfor (name in options) {\n\t\t\t\tsrc = getProperty(target, name);\n\t\t\t\tcopy = getProperty(options, name);\n\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif (target !== copy) {\n\t\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\t\tif (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n\t\t\t\t\t\tif (copyIsArray) {\n\t\t\t\t\t\t\tcopyIsArray = false;\n\t\t\t\t\t\t\tclone = src && isArray(src) ? src : [];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tclone = src && isPlainObject(src) ? src : {};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: extend(deep, clone, copy) });\n\n\t\t\t\t\t// Don't bring in undefined values\n\t\t\t\t\t} else if (typeof copy !== 'undefined') {\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: copy });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n", "/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */\nexport function bail(error) {\n  if (error) {\n    throw error\n  }\n}\n", "/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\nimport {bail} from 'bail'\nimport extend from 'extend'\nimport {ok as assert} from 'devlop'\nimport isPlainObj from 'is-plain-obj'\nimport {trough} from 'trough'\nimport {VFile} from 'vfile'\nimport {CallableInstance} from './callable-instance.js'\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nexport class Processor extends CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy')\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = []\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {}\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = trough()\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination =\n      /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ (\n        new Processor()\n      )\n    let index = -1\n\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index]\n      destination.use(...attacher)\n    }\n\n    destination.data(extend(true, {}, this.namespace))\n\n    return destination\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen)\n        this.namespace[key] = value\n        return this\n      }\n\n      // Get `key`.\n      return (own.call(this.namespace, key) && this.namespace[key]) || undefined\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen)\n      this.namespace = key\n      return this\n    }\n\n    // Get space.\n    return this.namespace\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ (/** @type {unknown} */ (this))\n\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      const transformer = attacher.call(self, ...options)\n\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer)\n      }\n    }\n\n    this.frozen = true\n    this.freezeIndex = Number.POSITIVE_INFINITY\n\n    return this\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const parser = this.parser || this.Parser\n    assertParser('parse', parser)\n    return parser(String(realFile), realFile)\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this\n\n    this.freeze()\n    assertParser('process', this.parser || this.Parser)\n    assertCompiler('process', this.compiler || this.Compiler)\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file)\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree =\n        /** @type {HeadTree extends undefined ? Node : HeadTree} */ (\n          /** @type {unknown} */ (self.parse(realFile))\n        )\n\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error)\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree =\n          /** @type {CompileTree extends undefined ? Node : CompileTree} */ (\n            /** @type {unknown} */ (tree)\n          )\n\n        const compileResult = self.stringify(compileTree, file)\n\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult\n        } else {\n          file.result = compileResult\n        }\n\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */ (file))\n      })\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result\n\n    this.freeze()\n    assertParser('processSync', this.parser || this.Parser)\n    assertCompiler('processSync', this.compiler || this.Compiler)\n\n    this.process(file, realDone)\n    assertDone('processSync', 'process', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n\n    return result\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true\n      bail(error)\n      result = file\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree)\n    this.freeze()\n\n    const transformers = this.transformers\n\n    if (!done && typeof file === 'function') {\n      done = file\n      file = undefined\n    }\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      assert(\n        typeof file !== 'function',\n        '`file` can’t be a `done` anymore, we checked'\n      )\n      const realFile = vfile(file)\n      transformers.run(tree, realFile, realDone)\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree =\n          /** @type {TailTree extends undefined ? Node : TailTree} */ (\n            outputTree || tree\n          )\n\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(resultingTree)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, resultingTree, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result\n\n    this.run(tree, file, realDone)\n\n    assertDone('runSync', 'run', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n    return result\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      bail(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const compiler = this.compiler || this.Compiler\n    assertCompiler('stringify', compiler)\n    assertNode(tree)\n\n    return compiler(tree, realFile)\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers\n    const namespace = this.namespace\n\n    assertUnfrozen('use', this.frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    return this\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, [])\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] =\n            /** @type {PluginTuple<Array<unknown>>} */ (value)\n          addPlugin(plugin, parameters)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error(\n          'Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither'\n        )\n      }\n\n      addList(result.plugins)\n\n      if (result.settings) {\n        namespace.settings = extend(true, namespace.settings, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1\n      let entryIndex = -1\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index\n          break\n        }\n      }\n\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters])\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters\n        const currentPrimary = attachers[entryIndex][1]\n        if (isPlainObj(currentPrimary) && isPlainObj(primary)) {\n          primary = extend(true, currentPrimary, primary)\n        }\n\n        attachers[entryIndex] = [plugin, primary, ...rest]\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nexport const unified = new Processor().freeze()\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value)\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value)\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n", "export default function isPlainObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\n\tconst prototype = Object.getPrototypeOf(value);\n\treturn (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\n", "// To do: remove `void`s\n// To do: remove `null` from output of our APIs, allow it as user APIs.\n\n/**\n * @typedef {(error?: Error | null | undefined, ...output: Array<any>) => void} Callback\n *   Callback.\n *\n * @typedef {(...input: Array<any>) => any} Middleware\n *   Ware.\n *\n * @typedef Pipeline\n *   Pipeline.\n * @property {Run} run\n *   Run the pipeline.\n * @property {Use} use\n *   Add middleware.\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n *\n *   Calls `done` on completion with either an error or the output of the\n *   last middleware.\n *\n *   > 👉 **Note**: as the length of input defines whether async functions get a\n *   > `next` function,\n *   > it’s recommended to keep `input` at one value normally.\n\n *\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n *   Pipeline.\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = []\n  /** @type {Pipeline} */\n  const pipeline = {run, use}\n\n  return pipeline\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1\n    /** @type {Callback} */\n    const callback = values.pop()\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback)\n    }\n\n    next(null, ...values)\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error | null | undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex]\n      let index = -1\n\n      if (error) {\n        callback(error)\n        return\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index]\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output)\n      } else {\n        callback(null, ...output)\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError(\n        'Expected `middelware` to be a function, not ' + middelware\n      )\n    }\n\n    fns.push(middelware)\n    return pipeline\n  }\n}\n\n/**\n * Wrap `middleware` into a uniform interface.\n *\n * You can pass all input to the resulting function.\n * `callback` is then called with the output of `middleware`.\n *\n * If `middleware` accepts more arguments than the later given in input,\n * an extra `done` function is passed to it after that input,\n * which must be called by `middleware`.\n *\n * The first value in `input` is the main input value.\n * All other input values are the rest input values.\n * The values given to `callback` are the input values,\n * merged with every non-nullish output value.\n *\n * * if `middleware` throws an error,\n *   returns a promise that is rejected,\n *   or calls the given `done` function with an error,\n *   `callback` is called with that error\n * * if `middleware` returns a value or returns a promise that is resolved,\n *   that value is the main output value\n * * if `middleware` calls `done`,\n *   all non-nullish values except for the first one (the error) overwrite the\n *   output values\n *\n * @param {Middleware} middleware\n *   Function to wrap.\n * @param {Callback} callback\n *   Callback called with the output of `middleware`.\n * @returns {Run}\n *   Wrapped middleware.\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called\n\n  return wrapped\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length\n    /** @type {any} */\n    let result\n\n    if (fnExpectsCallback) {\n      parameters.push(done)\n    }\n\n    try {\n      result = middleware.apply(this, parameters)\n    } catch (error) {\n      const exception = /** @type {Error} */ (error)\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception\n      }\n\n      return done(exception)\n    }\n\n    if (!fnExpectsCallback) {\n      if (result && result.then && typeof result.then === 'function') {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   *\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true\n      callback(error, ...output)\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value)\n  }\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Point | PointLike | Position | PositionLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef Options\n *   Configuration.\n * @property {Array<Node> | null | undefined} [ancestors]\n *   Stack of (inclusive) ancestor nodes surrounding the message (optional).\n * @property {Error | null | undefined} [cause]\n *   Original error cause of the message (optional).\n * @property {Point | Position | null | undefined} [place]\n *   Place of message (optional).\n * @property {string | null | undefined} [ruleId]\n *   Category of message (optional, example: `'my-rule'`).\n * @property {string | null | undefined} [source]\n *   Namespace of who sent the message (optional, example: `'my-package'`).\n */\n\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Options | null | undefined} [options]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | Options | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // eslint-disable-next-line complexity\n  constructor(causeOrReason, optionsOrParentOrPlace, origin) {\n    super()\n\n    if (typeof optionsOrParentOrPlace === 'string') {\n      origin = optionsOrParentOrPlace\n      optionsOrParentOrPlace = undefined\n    }\n\n    /** @type {string} */\n    let reason = ''\n    /** @type {Options} */\n    let options = {}\n    let legacyCause = false\n\n    if (optionsOrParentOrPlace) {\n      // Point.\n      if (\n        'line' in optionsOrParentOrPlace &&\n        'column' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Position.\n      else if (\n        'start' in optionsOrParentOrPlace &&\n        'end' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Node.\n      else if ('type' in optionsOrParentOrPlace) {\n        options = {\n          ancestors: [optionsOrParentOrPlace],\n          place: optionsOrParentOrPlace.position\n        }\n      }\n      // Options.\n      else {\n        options = {...optionsOrParentOrPlace}\n      }\n    }\n\n    if (typeof causeOrReason === 'string') {\n      reason = causeOrReason\n    }\n    // Error.\n    else if (!options.cause && causeOrReason) {\n      legacyCause = true\n      reason = causeOrReason.message\n      options.cause = causeOrReason\n    }\n\n    if (!options.ruleId && !options.source && typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        options.ruleId = origin\n      } else {\n        options.source = origin.slice(0, index)\n        options.ruleId = origin.slice(index + 1)\n      }\n    }\n\n    if (!options.place && options.ancestors && options.ancestors) {\n      const parent = options.ancestors[options.ancestors.length - 1]\n\n      if (parent) {\n        options.place = parent.position\n      }\n    }\n\n    const start =\n      options.place && 'start' in options.place\n        ? options.place.start\n        : options.place\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Stack of ancestor nodes surrounding the message.\n     *\n     * @type {Array<Node> | undefined}\n     */\n    this.ancestors = options.ancestors || undefined\n\n    /**\n     * Original error cause of the message.\n     *\n     * @type {Error | undefined}\n     */\n    this.cause = options.cause || undefined\n\n    /**\n     * Starting column of message.\n     *\n     * @type {number | undefined}\n     */\n    this.column = start ? start.column : undefined\n\n    /**\n     * State of problem.\n     *\n     * * `true` — error, file not usable\n     * * `false` — warning, change may be needed\n     * * `undefined` — change likely not needed\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal = undefined\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | undefined}\n     */\n    this.file\n\n    // Field from `Error`.\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = reason\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | undefined}\n     */\n    this.line = start ? start.line : undefined\n\n    // Field from `Error`.\n    /**\n     * Serialized positional info of message.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(options.place) || '1:1'\n\n    /**\n     * Place of message.\n     *\n     * @type {Point | Position | undefined}\n     */\n    this.place = options.place || undefined\n\n    /**\n     * Reason for message, should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | undefined}\n     */\n    this.ruleId = options.ruleId || undefined\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | undefined}\n     */\n    this.source = options.source || undefined\n\n    // Field from `Error`.\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack =\n      legacyCause && options.cause && typeof options.cause.stack === 'string'\n        ? options.cause.stack\n        : ''\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | undefined}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | undefined}\n     */\n    this.expected\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | undefined}\n     */\n    this.note\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | undefined}\n     */\n    this.url\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.column = undefined\nVFileMessage.prototype.line = undefined\nVFileMessage.prototype.ancestors = undefined\nVFileMessage.prototype.cause = undefined\nVFileMessage.prototype.fatal = undefined\nVFileMessage.prototype.place = undefined\nVFileMessage.prototype.ruleId = undefined\nVFileMessage.prototype.source = undefined\n", "// A derivative work based on:\n// <https://github.com/browserify/path-browserify>.\n// Which is licensed:\n//\n// MIT License\n//\n// Copyright (c) 2013 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n// subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A derivative work based on:\n//\n// Parts of that are extracted from Node’s internal `path` module:\n// <https://github.com/nodejs/node/blob/master/lib/path.js>.\n// Which is licensed:\n//\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nexport const minpath = {basename, dirname, extname, join, sep: '/'}\n\n/* eslint-disable max-depth, complexity */\n\n/**\n * Get the basename from a path.\n *\n * @param {string} path\n *   File path.\n * @param {string | null | undefined} [extname]\n *   Extension to strip.\n * @returns {string}\n *   Stem or basename.\n */\nfunction basename(path, extname) {\n  if (extname !== undefined && typeof extname !== 'string') {\n    throw new TypeError('\"ext\" argument must be a string')\n  }\n\n  assertPath(path)\n  let start = 0\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let seenNonSlash\n\n  if (\n    extname === undefined ||\n    extname.length === 0 ||\n    extname.length > path.length\n  ) {\n    while (index--) {\n      if (path.codePointAt(index) === 47 /* `/` */) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now.\n        if (seenNonSlash) {\n          start = index + 1\n          break\n        }\n      } else if (end < 0) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component.\n        seenNonSlash = true\n        end = index + 1\n      }\n    }\n\n    return end < 0 ? '' : path.slice(start, end)\n  }\n\n  if (extname === path) {\n    return ''\n  }\n\n  let firstNonSlashEnd = -1\n  let extnameIndex = extname.length - 1\n\n  while (index--) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (seenNonSlash) {\n        start = index + 1\n        break\n      }\n    } else {\n      if (firstNonSlashEnd < 0) {\n        // We saw the first non-path separator, remember this index in case\n        // we need it if the extension ends up not matching.\n        seenNonSlash = true\n        firstNonSlashEnd = index + 1\n      }\n\n      if (extnameIndex > -1) {\n        // Try to match the explicit extension.\n        if (path.codePointAt(index) === extname.codePointAt(extnameIndex--)) {\n          if (extnameIndex < 0) {\n            // We matched the extension, so mark this as the end of our path\n            // component\n            end = index\n          }\n        } else {\n          // Extension does not match, so our result is the entire path\n          // component\n          extnameIndex = -1\n          end = firstNonSlashEnd\n        }\n      }\n    }\n  }\n\n  if (start === end) {\n    end = firstNonSlashEnd\n  } else if (end < 0) {\n    end = path.length\n  }\n\n  return path.slice(start, end)\n}\n\n/**\n * Get the dirname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\nfunction dirname(path) {\n  assertPath(path)\n\n  if (path.length === 0) {\n    return '.'\n  }\n\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  // Prefix `--` is important to not run on `0`.\n  while (--index) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      if (unmatchedSlash) {\n        end = index\n        break\n      }\n    } else if (!unmatchedSlash) {\n      // We saw the first non-path separator\n      unmatchedSlash = true\n    }\n  }\n\n  return end < 0\n    ? path.codePointAt(0) === 47 /* `/` */\n      ? '/'\n      : '.'\n    : end === 1 && path.codePointAt(0) === 47 /* `/` */\n      ? '//'\n      : path.slice(0, end)\n}\n\n/**\n * Get an extname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   Extname.\n */\nfunction extname(path) {\n  assertPath(path)\n\n  let index = path.length\n\n  let end = -1\n  let startPart = 0\n  let startDot = -1\n  // Track the state of characters (if any) we see before our first dot and\n  // after any path separator we find.\n  let preDotState = 0\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  while (index--) {\n    const code = path.codePointAt(index)\n\n    if (code === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (unmatchedSlash) {\n        startPart = index + 1\n        break\n      }\n\n      continue\n    }\n\n    if (end < 0) {\n      // We saw the first non-path separator, mark this as the end of our\n      // extension.\n      unmatchedSlash = true\n      end = index + 1\n    }\n\n    if (code === 46 /* `.` */) {\n      // If this is our first dot, mark it as the start of our extension.\n      if (startDot < 0) {\n        startDot = index\n      } else if (preDotState !== 1) {\n        preDotState = 1\n      }\n    } else if (startDot > -1) {\n      // We saw a non-dot and non-path separator before our dot, so we should\n      // have a good chance at having a non-empty extension.\n      preDotState = -1\n    }\n  }\n\n  if (\n    startDot < 0 ||\n    end < 0 ||\n    // We saw a non-dot character immediately before the dot.\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly `..`.\n    (preDotState === 1 && startDot === end - 1 && startDot === startPart + 1)\n  ) {\n    return ''\n  }\n\n  return path.slice(startDot, end)\n}\n\n/**\n * Join segments from a path.\n *\n * @param {Array<string>} segments\n *   Path segments.\n * @returns {string}\n *   File path.\n */\nfunction join(...segments) {\n  let index = -1\n  /** @type {string | undefined} */\n  let joined\n\n  while (++index < segments.length) {\n    assertPath(segments[index])\n\n    if (segments[index]) {\n      joined =\n        joined === undefined ? segments[index] : joined + '/' + segments[index]\n    }\n  }\n\n  return joined === undefined ? '.' : normalize(joined)\n}\n\n/**\n * Normalize a basic file path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\n// Note: `normalize` is not exposed as `path.normalize`, so some code is\n// manually removed from it.\nfunction normalize(path) {\n  assertPath(path)\n\n  const absolute = path.codePointAt(0) === 47 /* `/` */\n\n  // Normalize the path according to POSIX rules.\n  let value = normalizeString(path, !absolute)\n\n  if (value.length === 0 && !absolute) {\n    value = '.'\n  }\n\n  if (value.length > 0 && path.codePointAt(path.length - 1) === 47 /* / */) {\n    value += '/'\n  }\n\n  return absolute ? '/' + value : value\n}\n\n/**\n * Resolve `.` and `..` elements in a path with directory names.\n *\n * @param {string} path\n *   File path.\n * @param {boolean} allowAboveRoot\n *   Whether `..` can move above root.\n * @returns {string}\n *   File path.\n */\nfunction normalizeString(path, allowAboveRoot) {\n  let result = ''\n  let lastSegmentLength = 0\n  let lastSlash = -1\n  let dots = 0\n  let index = -1\n  /** @type {number | undefined} */\n  let code\n  /** @type {number} */\n  let lastSlashIndex\n\n  while (++index <= path.length) {\n    if (index < path.length) {\n      code = path.codePointAt(index)\n    } else if (code === 47 /* `/` */) {\n      break\n    } else {\n      code = 47 /* `/` */\n    }\n\n    if (code === 47 /* `/` */) {\n      if (lastSlash === index - 1 || dots === 1) {\n        // Empty.\n      } else if (lastSlash !== index - 1 && dots === 2) {\n        if (\n          result.length < 2 ||\n          lastSegmentLength !== 2 ||\n          result.codePointAt(result.length - 1) !== 46 /* `.` */ ||\n          result.codePointAt(result.length - 2) !== 46 /* `.` */\n        ) {\n          if (result.length > 2) {\n            lastSlashIndex = result.lastIndexOf('/')\n\n            if (lastSlashIndex !== result.length - 1) {\n              if (lastSlashIndex < 0) {\n                result = ''\n                lastSegmentLength = 0\n              } else {\n                result = result.slice(0, lastSlashIndex)\n                lastSegmentLength = result.length - 1 - result.lastIndexOf('/')\n              }\n\n              lastSlash = index\n              dots = 0\n              continue\n            }\n          } else if (result.length > 0) {\n            result = ''\n            lastSegmentLength = 0\n            lastSlash = index\n            dots = 0\n            continue\n          }\n        }\n\n        if (allowAboveRoot) {\n          result = result.length > 0 ? result + '/..' : '..'\n          lastSegmentLength = 2\n        }\n      } else {\n        if (result.length > 0) {\n          result += '/' + path.slice(lastSlash + 1, index)\n        } else {\n          result = path.slice(lastSlash + 1, index)\n        }\n\n        lastSegmentLength = index - lastSlash - 1\n      }\n\n      lastSlash = index\n      dots = 0\n    } else if (code === 46 /* `.` */ && dots > -1) {\n      dots++\n    } else {\n      dots = -1\n    }\n  }\n\n  return result\n}\n\n/**\n * Make sure `path` is a string.\n *\n * @param {string} path\n *   File path.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError(\n      'Path must be a string. Received ' + JSON.stringify(path)\n    )\n  }\n}\n\n/* eslint-enable max-depth, complexity */\n", "// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nexport const minproc = {cwd}\n\nfunction cwd() {\n  return '/'\n}\n", "/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * We check for auth attribute to distinguish legacy url instance with\n * WHATWG URL instance.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js#L720>\nexport function isUrl(fileUrlOrPath) {\n  return Boolean(\n    fileUrlOrPath !== null &&\n      typeof fileUrlOrPath === 'object' &&\n      'href' in fileUrlOrPath &&\n      fileUrlOrPath.href &&\n      'protocol' in fileUrlOrPath &&\n      fileUrlOrPath.protocol &&\n      // @ts-expect-error: indexing is fine.\n      fileUrlOrPath.auth === undefined\n  )\n}\n", "import {isUrl} from './minurl.shared.js'\n\nexport {isUrl} from './minurl.shared.js'\n\n// See: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js>\n\n/**\n * @param {URL | string} path\n *   File URL.\n * @returns {string}\n *   File URL.\n */\nexport function urlToPath(path) {\n  if (typeof path === 'string') {\n    path = new URL(path)\n  } else if (!isUrl(path)) {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'The \"path\" argument must be of type string or an instance of URL. Received `' +\n        path +\n        '`'\n    )\n    error.code = 'ERR_INVALID_ARG_TYPE'\n    throw error\n  }\n\n  if (path.protocol !== 'file:') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The URL must be of scheme file')\n    error.code = 'ERR_INVALID_URL_SCHEME'\n    throw error\n  }\n\n  return getPathFromURLPosix(path)\n}\n\n/**\n * Get a path from a POSIX URL.\n *\n * @param {URL} url\n *   URL.\n * @returns {string}\n *   File path.\n */\nfunction getPathFromURLPosix(url) {\n  if (url.hostname !== '') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'File URL host must be \"localhost\" or empty on darwin'\n    )\n    error.code = 'ERR_INVALID_FILE_URL_HOST'\n    throw error\n  }\n\n  const pathname = url.pathname\n  let index = -1\n\n  while (++index < pathname.length) {\n    if (\n      pathname.codePointAt(index) === 37 /* `%` */ &&\n      pathname.codePointAt(index + 1) === 50 /* `2` */\n    ) {\n      const third = pathname.codePointAt(index + 2)\n      if (third === 70 /* `F` */ || third === 102 /* `f` */) {\n        /** @type {NodeJS.ErrnoException} */\n        const error = new TypeError(\n          'File URL path must not include encoded / characters'\n        )\n        error.code = 'ERR_INVALID_FILE_URL_PATH'\n        throw error\n      }\n    }\n  }\n\n  return decodeURIComponent(pathname)\n}\n", "/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport {VFileMessage} from 'vfile-message'\nimport {minpath} from '#minpath'\nimport {minproc} from '#minproc'\nimport {urlToPath, isUrl} from '#minurl'\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */ ([\n  'history',\n  'path',\n  'basename',\n  'stem',\n  'extname',\n  'dirname'\n])\n\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (isUrl(value)) {\n      options = {path: value}\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {value}\n    } else {\n      options = value\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : minproc.cwd()\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const field = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        field in options &&\n        options[field] !== undefined &&\n        options[field] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field]\n      }\n    }\n\n    /** @type {string} */\n    let field\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field]\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path)\n      : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = minpath.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string'\n      ? minpath.dirname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = minpath.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string'\n      ? minpath.extname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = minpath.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = minpath.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = undefined\n\n    return message\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new VFileMessage(\n      // @ts-expect-error: the overloads are fine.\n      causeOrReason,\n      optionsOrParentOrPlace,\n      origin\n    )\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return ''\n    }\n\n    if (typeof this.value === 'string') {\n      return this.value\n    }\n\n    const decoder = new TextDecoder(encoding || undefined)\n    return decoder.decode(this.value)\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(minpath.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + minpath.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n", "export const CallableInstance =\n  /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */\n  (\n    /** @type {unknown} */\n    (\n      /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */\n      function (property) {\n        const self = this\n        const constr = self.constructor\n        const proto = /** @type {Record<string | symbol, Function>} */ (\n          // Prototypes do exist.\n          // type-coverage:ignore-next-line\n          constr.prototype\n        )\n        const value = proto[property]\n        /** @type {(...parameters: Array<unknown>) => unknown} */\n        const apply = function () {\n          return value.apply(apply, arguments)\n        }\n\n        Object.setPrototypeOf(apply, proto)\n\n        // Not needed for us in `unified`: we only call this on the `copy`\n        // function,\n        // and we don't need to add its fields (`length`, `name`)\n        // over.\n        // See also: GH-246.\n        // const names = Object.getOwnPropertyNames(value)\n        //\n        // for (const p of names) {\n        //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n        //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n        // }\n\n        return apply\n      }\n    )\n  )\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,SAAS,OAAO,UAAU;AAC9B,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,iBAAiB,OAAO;AAC5B,QAAI,OAAO,OAAO;AAElB,QAAI,UAAU,SAASA,SAAQ,KAAK;AACnC,UAAI,OAAO,MAAM,YAAY,YAAY;AACxC,eAAO,MAAM,QAAQ,GAAG;AAAA,MACzB;AAEA,aAAO,MAAM,KAAK,GAAG,MAAM;AAAA,IAC5B;AAEA,QAAIC,iBAAgB,SAASA,eAAc,KAAK;AAC/C,UAAI,CAAC,OAAO,MAAM,KAAK,GAAG,MAAM,mBAAmB;AAClD,eAAO;AAAA,MACR;AAEA,UAAI,oBAAoB,OAAO,KAAK,KAAK,aAAa;AACtD,UAAI,mBAAmB,IAAI,eAAe,IAAI,YAAY,aAAa,OAAO,KAAK,IAAI,YAAY,WAAW,eAAe;AAE7H,UAAI,IAAI,eAAe,CAAC,qBAAqB,CAAC,kBAAkB;AAC/D,eAAO;AAAA,MACR;AAIA,UAAI;AACJ,WAAK,OAAO,KAAK;AAAA,MAAO;AAExB,aAAO,OAAO,QAAQ,eAAe,OAAO,KAAK,KAAK,GAAG;AAAA,IAC1D;AAGA,QAAI,cAAc,SAASC,aAAY,QAAQ,SAAS;AACvD,UAAI,kBAAkB,QAAQ,SAAS,aAAa;AACnD,uBAAe,QAAQ,QAAQ,MAAM;AAAA,UACpC,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO,QAAQ;AAAA,UACf,UAAU;AAAA,QACX,CAAC;AAAA,MACF,OAAO;AACN,eAAO,QAAQ,IAAI,IAAI,QAAQ;AAAA,MAChC;AAAA,IACD;AAGA,QAAI,cAAc,SAASC,aAAY,KAAK,MAAM;AACjD,UAAI,SAAS,aAAa;AACzB,YAAI,CAAC,OAAO,KAAK,KAAK,IAAI,GAAG;AAC5B,iBAAO;AAAA,QACR,WAAW,MAAM;AAGhB,iBAAO,KAAK,KAAK,IAAI,EAAE;AAAA,QACxB;AAAA,MACD;AAEA,aAAO,IAAI,IAAI;AAAA,IAChB;AAEA,WAAO,UAAU,SAASC,UAAS;AAClC,UAAI,SAAS,MAAM,KAAK,MAAM,aAAa;AAC3C,UAAI,SAAS,UAAU,CAAC;AACxB,UAAI,IAAI;AACR,UAAI,SAAS,UAAU;AACvB,UAAI,OAAO;AAGX,UAAI,OAAO,WAAW,WAAW;AAChC,eAAO;AACP,iBAAS,UAAU,CAAC,KAAK,CAAC;AAE1B,YAAI;AAAA,MACL;AACA,UAAI,UAAU,QAAS,OAAO,WAAW,YAAY,OAAO,WAAW,YAAa;AACnF,iBAAS,CAAC;AAAA,MACX;AAEA,aAAO,IAAI,QAAQ,EAAE,GAAG;AACvB,kBAAU,UAAU,CAAC;AAErB,YAAI,WAAW,MAAM;AAEpB,eAAK,QAAQ,SAAS;AACrB,kBAAM,YAAY,QAAQ,IAAI;AAC9B,mBAAO,YAAY,SAAS,IAAI;AAGhC,gBAAI,WAAW,MAAM;AAEpB,kBAAI,QAAQ,SAASH,eAAc,IAAI,MAAM,cAAc,QAAQ,IAAI,KAAK;AAC3E,oBAAI,aAAa;AAChB,gCAAc;AACd,0BAAQ,OAAO,QAAQ,GAAG,IAAI,MAAM,CAAC;AAAA,gBACtC,OAAO;AACN,0BAAQ,OAAOA,eAAc,GAAG,IAAI,MAAM,CAAC;AAAA,gBAC5C;AAGA,4BAAY,QAAQ,EAAE,MAAY,UAAUG,QAAO,MAAM,OAAO,IAAI,EAAE,CAAC;AAAA,cAGxE,WAAW,OAAO,SAAS,aAAa;AACvC,4BAAY,QAAQ,EAAE,MAAY,UAAU,KAAK,CAAC;AAAA,cACnD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAGA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC7GO,SAAS,KAAK,OAAO;AAC1B,MAAI,OAAO;AACT,UAAM;AAAA,EACR;AACF;;;ACkVA,oBAAmB;;;AC7VJ,SAAR,cAA+B,OAAO;AAC5C,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,WAAO;AAAA,EACR;AAEA,QAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,UAAQ,cAAc,QAAQ,cAAc,OAAO,aAAa,OAAO,eAAe,SAAS,MAAM,SAAS,EAAE,OAAO,eAAe,UAAU,EAAE,OAAO,YAAY;AACtK;;;AC+BO,SAAS,SAAS;AAEvB,QAAM,MAAM,CAAC;AAEb,QAAM,WAAW,EAAC,KAAK,IAAG;AAE1B,SAAO;AAGP,WAAS,OAAO,QAAQ;AACtB,QAAI,kBAAkB;AAEtB,UAAM,WAAW,OAAO,IAAI;AAE5B,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,IAAI,UAAU,6CAA6C,QAAQ;AAAA,IAC3E;AAEA,SAAK,MAAM,GAAG,MAAM;AAQpB,aAAS,KAAK,UAAU,QAAQ;AAC9B,YAAM,KAAK,IAAI,EAAE,eAAe;AAChC,UAAIC,SAAQ;AAEZ,UAAI,OAAO;AACT,iBAAS,KAAK;AACd;AAAA,MACF;AAGA,aAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,YAAI,OAAOA,MAAK,MAAM,QAAQ,OAAOA,MAAK,MAAM,QAAW;AACzD,iBAAOA,MAAK,IAAI,OAAOA,MAAK;AAAA,QAC9B;AAAA,MACF;AAGA,eAAS;AAGT,UAAI,IAAI;AACN,aAAK,IAAI,IAAI,EAAE,GAAG,MAAM;AAAA,MAC1B,OAAO;AACL,iBAAS,MAAM,GAAG,MAAM;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAGA,WAAS,IAAI,YAAY;AACvB,QAAI,OAAO,eAAe,YAAY;AACpC,YAAM,IAAI;AAAA,QACR,iDAAiD;AAAA,MACnD;AAAA,IACF;AAEA,QAAI,KAAK,UAAU;AACnB,WAAO;AAAA,EACT;AACF;AAkCO,SAAS,KAAK,YAAY,UAAU;AAEzC,MAAI;AAEJ,SAAO;AAQP,WAAS,WAAW,YAAY;AAC9B,UAAM,oBAAoB,WAAW,SAAS,WAAW;AAEzD,QAAI;AAEJ,QAAI,mBAAmB;AACrB,iBAAW,KAAK,IAAI;AAAA,IACtB;AAEA,QAAI;AACF,eAAS,WAAW,MAAM,MAAM,UAAU;AAAA,IAC5C,SAAS,OAAO;AACd,YAAM;AAAA;AAAA,QAAkC;AAAA;AAMxC,UAAI,qBAAqB,QAAQ;AAC/B,cAAM;AAAA,MACR;AAEA,aAAO,KAAK,SAAS;AAAA,IACvB;AAEA,QAAI,CAAC,mBAAmB;AACtB,UAAI,UAAU,OAAO,QAAQ,OAAO,OAAO,SAAS,YAAY;AAC9D,eAAO,KAAK,MAAM,IAAI;AAAA,MACxB,WAAW,kBAAkB,OAAO;AAClC,aAAK,MAAM;AAAA,MACb,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAOA,WAAS,KAAK,UAAU,QAAQ;AAC9B,QAAI,CAAC,QAAQ;AACX,eAAS;AACT,eAAS,OAAO,GAAG,MAAM;AAAA,IAC3B;AAAA,EACF;AAOA,WAAS,KAAK,OAAO;AACnB,SAAK,MAAM,KAAK;AAAA,EAClB;AACF;;;ACzKO,SAAS,kBAAkB,OAAO;AAEvC,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,WAAO;AAAA,EACT;AAGA,MAAI,cAAc,SAAS,UAAU,OAAO;AAC1C,WAAO,SAAS,MAAM,QAAQ;AAAA,EAChC;AAGA,MAAI,WAAW,SAAS,SAAS,OAAO;AACtC,WAAO,SAAS,KAAK;AAAA,EACvB;AAGA,MAAI,UAAU,SAAS,YAAY,OAAO;AACxC,WAAO,MAAM,KAAK;AAAA,EACpB;AAGA,SAAO;AACT;AAMA,SAAS,MAAMC,QAAO;AACpB,SAAO,MAAMA,UAASA,OAAM,IAAI,IAAI,MAAM,MAAMA,UAASA,OAAM,MAAM;AACvE;AAMA,SAAS,SAAS,KAAK;AACrB,SAAO,MAAM,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI,GAAG;AAC7D;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,SAAS,OAAO,UAAU,WAAW,QAAQ;AACtD;;;ACvDO,IAAM,eAAN,cAA2B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwDtC,YAAY,eAAe,wBAAwB,QAAQ;AACzD,UAAM;AAEN,QAAI,OAAO,2BAA2B,UAAU;AAC9C,eAAS;AACT,+BAAyB;AAAA,IAC3B;AAGA,QAAI,SAAS;AAEb,QAAI,UAAU,CAAC;AACf,QAAI,cAAc;AAElB,QAAI,wBAAwB;AAE1B,UACE,UAAU,0BACV,YAAY,wBACZ;AACA,kBAAU,EAAC,OAAO,uBAAsB;AAAA,MAC1C,WAGE,WAAW,0BACX,SAAS,wBACT;AACA,kBAAU,EAAC,OAAO,uBAAsB;AAAA,MAC1C,WAES,UAAU,wBAAwB;AACzC,kBAAU;AAAA,UACR,WAAW,CAAC,sBAAsB;AAAA,UAClC,OAAO,uBAAuB;AAAA,QAChC;AAAA,MACF,OAEK;AACH,kBAAU,EAAC,GAAG,uBAAsB;AAAA,MACtC;AAAA,IACF;AAEA,QAAI,OAAO,kBAAkB,UAAU;AACrC,eAAS;AAAA,IACX,WAES,CAAC,QAAQ,SAAS,eAAe;AACxC,oBAAc;AACd,eAAS,cAAc;AACvB,cAAQ,QAAQ;AAAA,IAClB;AAEA,QAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,UAAU,OAAO,WAAW,UAAU;AACpE,YAAMC,SAAQ,OAAO,QAAQ,GAAG;AAEhC,UAAIA,WAAU,IAAI;AAChB,gBAAQ,SAAS;AAAA,MACnB,OAAO;AACL,gBAAQ,SAAS,OAAO,MAAM,GAAGA,MAAK;AACtC,gBAAQ,SAAS,OAAO,MAAMA,SAAQ,CAAC;AAAA,MACzC;AAAA,IACF;AAEA,QAAI,CAAC,QAAQ,SAAS,QAAQ,aAAa,QAAQ,WAAW;AAC5D,YAAM,SAAS,QAAQ,UAAU,QAAQ,UAAU,SAAS,CAAC;AAE7D,UAAI,QAAQ;AACV,gBAAQ,QAAQ,OAAO;AAAA,MACzB;AAAA,IACF;AAEA,UAAM,QACJ,QAAQ,SAAS,WAAW,QAAQ,QAChC,QAAQ,MAAM,QACd,QAAQ;AAQd,SAAK,YAAY,QAAQ,aAAa;AAOtC,SAAK,QAAQ,QAAQ,SAAS;AAO9B,SAAK,SAAS,QAAQ,MAAM,SAAS;AAWrC,SAAK,QAAQ;AAOb,SAAK;AAQL,SAAK,UAAU;AAOf,SAAK,OAAO,QAAQ,MAAM,OAAO;AASjC,SAAK,OAAO,kBAAkB,QAAQ,KAAK,KAAK;AAOhD,SAAK,QAAQ,QAAQ,SAAS;AAO9B,SAAK,SAAS,KAAK;AAOnB,SAAK,SAAS,QAAQ,UAAU;AAOhC,SAAK,SAAS,QAAQ,UAAU;AAWhC,SAAK,QACH,eAAe,QAAQ,SAAS,OAAO,QAAQ,MAAM,UAAU,WAC3D,QAAQ,MAAM,QACd;AAYN,SAAK;AAOL,SAAK;AAOL,SAAK;AAUL,SAAK;AAAA,EAEP;AACF;AAEA,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,SAAS;AAChC,aAAa,UAAU,UAAU;AACjC,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,SAAS;AAChC,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,YAAY;AACnC,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,SAAS;AAChC,aAAa,UAAU,SAAS;;;AC1QzB,IAAM,UAAU,EAAC,UAAU,SAAS,SAAS,MAAM,KAAK,IAAG;AAclE,SAAS,SAAS,MAAMC,UAAS;AAC/B,MAAIA,aAAY,UAAa,OAAOA,aAAY,UAAU;AACxD,UAAM,IAAI,UAAU,iCAAiC;AAAA,EACvD;AAEA,aAAW,IAAI;AACf,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,MAAIC,SAAQ,KAAK;AAEjB,MAAI;AAEJ,MACED,aAAY,UACZA,SAAQ,WAAW,KACnBA,SAAQ,SAAS,KAAK,QACtB;AACA,WAAOC,UAAS;AACd,UAAI,KAAK,YAAYA,MAAK,MAAM,IAAc;AAG5C,YAAI,cAAc;AAChB,kBAAQA,SAAQ;AAChB;AAAA,QACF;AAAA,MACF,WAAW,MAAM,GAAG;AAGlB,uBAAe;AACf,cAAMA,SAAQ;AAAA,MAChB;AAAA,IACF;AAEA,WAAO,MAAM,IAAI,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,EAC7C;AAEA,MAAID,aAAY,MAAM;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB;AACvB,MAAI,eAAeA,SAAQ,SAAS;AAEpC,SAAOC,UAAS;AACd,QAAI,KAAK,YAAYA,MAAK,MAAM,IAAc;AAG5C,UAAI,cAAc;AAChB,gBAAQA,SAAQ;AAChB;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,mBAAmB,GAAG;AAGxB,uBAAe;AACf,2BAAmBA,SAAQ;AAAA,MAC7B;AAEA,UAAI,eAAe,IAAI;AAErB,YAAI,KAAK,YAAYA,MAAK,MAAMD,SAAQ,YAAY,cAAc,GAAG;AACnE,cAAI,eAAe,GAAG;AAGpB,kBAAMC;AAAA,UACR;AAAA,QACF,OAAO;AAGL,yBAAe;AACf,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,UAAU,KAAK;AACjB,UAAM;AAAA,EACR,WAAW,MAAM,GAAG;AAClB,UAAM,KAAK;AAAA,EACb;AAEA,SAAO,KAAK,MAAM,OAAO,GAAG;AAC9B;AAUA,SAAS,QAAQ,MAAM;AACrB,aAAW,IAAI;AAEf,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM;AACV,MAAIA,SAAQ,KAAK;AAEjB,MAAI;AAGJ,SAAO,EAAEA,QAAO;AACd,QAAI,KAAK,YAAYA,MAAK,MAAM,IAAc;AAC5C,UAAI,gBAAgB;AAClB,cAAMA;AACN;AAAA,MACF;AAAA,IACF,WAAW,CAAC,gBAAgB;AAE1B,uBAAiB;AAAA,IACnB;AAAA,EACF;AAEA,SAAO,MAAM,IACT,KAAK,YAAY,CAAC,MAAM,KACtB,MACA,MACF,QAAQ,KAAK,KAAK,YAAY,CAAC,MAAM,KACnC,OACA,KAAK,MAAM,GAAG,GAAG;AACzB;AAUA,SAAS,QAAQ,MAAM;AACrB,aAAW,IAAI;AAEf,MAAIA,SAAQ,KAAK;AAEjB,MAAI,MAAM;AACV,MAAI,YAAY;AAChB,MAAI,WAAW;AAGf,MAAI,cAAc;AAElB,MAAI;AAEJ,SAAOA,UAAS;AACd,UAAM,OAAO,KAAK,YAAYA,MAAK;AAEnC,QAAI,SAAS,IAAc;AAGzB,UAAI,gBAAgB;AAClB,oBAAYA,SAAQ;AACpB;AAAA,MACF;AAEA;AAAA,IACF;AAEA,QAAI,MAAM,GAAG;AAGX,uBAAiB;AACjB,YAAMA,SAAQ;AAAA,IAChB;AAEA,QAAI,SAAS,IAAc;AAEzB,UAAI,WAAW,GAAG;AAChB,mBAAWA;AAAA,MACb,WAAW,gBAAgB,GAAG;AAC5B,sBAAc;AAAA,MAChB;AAAA,IACF,WAAW,WAAW,IAAI;AAGxB,oBAAc;AAAA,IAChB;AAAA,EACF;AAEA,MACE,WAAW,KACX,MAAM;AAAA,EAEN,gBAAgB;AAAA,EAEf,gBAAgB,KAAK,aAAa,MAAM,KAAK,aAAa,YAAY,GACvE;AACA,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,UAAU,GAAG;AACjC;AAUA,SAAS,QAAQ,UAAU;AACzB,MAAIA,SAAQ;AAEZ,MAAI;AAEJ,SAAO,EAAEA,SAAQ,SAAS,QAAQ;AAChC,eAAW,SAASA,MAAK,CAAC;AAE1B,QAAI,SAASA,MAAK,GAAG;AACnB,eACE,WAAW,SAAY,SAASA,MAAK,IAAI,SAAS,MAAM,SAASA,MAAK;AAAA,IAC1E;AAAA,EACF;AAEA,SAAO,WAAW,SAAY,MAAM,UAAU,MAAM;AACtD;AAYA,SAAS,UAAU,MAAM;AACvB,aAAW,IAAI;AAEf,QAAM,WAAW,KAAK,YAAY,CAAC,MAAM;AAGzC,MAAI,QAAQ,gBAAgB,MAAM,CAAC,QAAQ;AAE3C,MAAI,MAAM,WAAW,KAAK,CAAC,UAAU;AACnC,YAAQ;AAAA,EACV;AAEA,MAAI,MAAM,SAAS,KAAK,KAAK,YAAY,KAAK,SAAS,CAAC,MAAM,IAAY;AACxE,aAAS;AAAA,EACX;AAEA,SAAO,WAAW,MAAM,QAAQ;AAClC;AAYA,SAAS,gBAAgB,MAAM,gBAAgB;AAC7C,MAAI,SAAS;AACb,MAAI,oBAAoB;AACxB,MAAI,YAAY;AAChB,MAAI,OAAO;AACX,MAAIA,SAAQ;AAEZ,MAAI;AAEJ,MAAI;AAEJ,SAAO,EAAEA,UAAS,KAAK,QAAQ;AAC7B,QAAIA,SAAQ,KAAK,QAAQ;AACvB,aAAO,KAAK,YAAYA,MAAK;AAAA,IAC/B,WAAW,SAAS,IAAc;AAChC;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,IAAc;AACzB,UAAI,cAAcA,SAAQ,KAAK,SAAS,GAAG;AAAA,MAE3C,WAAW,cAAcA,SAAQ,KAAK,SAAS,GAAG;AAChD,YACE,OAAO,SAAS,KAChB,sBAAsB,KACtB,OAAO,YAAY,OAAO,SAAS,CAAC,MAAM,MAC1C,OAAO,YAAY,OAAO,SAAS,CAAC,MAAM,IAC1C;AACA,cAAI,OAAO,SAAS,GAAG;AACrB,6BAAiB,OAAO,YAAY,GAAG;AAEvC,gBAAI,mBAAmB,OAAO,SAAS,GAAG;AACxC,kBAAI,iBAAiB,GAAG;AACtB,yBAAS;AACT,oCAAoB;AAAA,cACtB,OAAO;AACL,yBAAS,OAAO,MAAM,GAAG,cAAc;AACvC,oCAAoB,OAAO,SAAS,IAAI,OAAO,YAAY,GAAG;AAAA,cAChE;AAEA,0BAAYA;AACZ,qBAAO;AACP;AAAA,YACF;AAAA,UACF,WAAW,OAAO,SAAS,GAAG;AAC5B,qBAAS;AACT,gCAAoB;AACpB,wBAAYA;AACZ,mBAAO;AACP;AAAA,UACF;AAAA,QACF;AAEA,YAAI,gBAAgB;AAClB,mBAAS,OAAO,SAAS,IAAI,SAAS,QAAQ;AAC9C,8BAAoB;AAAA,QACtB;AAAA,MACF,OAAO;AACL,YAAI,OAAO,SAAS,GAAG;AACrB,oBAAU,MAAM,KAAK,MAAM,YAAY,GAAGA,MAAK;AAAA,QACjD,OAAO;AACL,mBAAS,KAAK,MAAM,YAAY,GAAGA,MAAK;AAAA,QAC1C;AAEA,4BAAoBA,SAAQ,YAAY;AAAA,MAC1C;AAEA,kBAAYA;AACZ,aAAO;AAAA,IACT,WAAW,SAAS,MAAgB,OAAO,IAAI;AAC7C;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAAS,WAAW,MAAM;AACxB,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,IAAI;AAAA,MACR,qCAAqC,KAAK,UAAU,IAAI;AAAA,IAC1D;AAAA,EACF;AACF;;;ACpaO,IAAM,UAAU,EAAC,IAAG;AAE3B,SAAS,MAAM;AACb,SAAO;AACT;;;ACYO,SAAS,MAAM,eAAe;AACnC,SAAO;AAAA,IACL,kBAAkB,QAChB,OAAO,kBAAkB,YACzB,UAAU,iBACV,cAAc,QACd,cAAc,iBACd,cAAc;AAAA,IAEd,cAAc,SAAS;AAAA,EAC3B;AACF;;;AClBO,SAAS,UAAU,MAAM;AAC9B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB,WAAW,CAAC,MAAM,IAAI,GAAG;AAEvB,UAAM,QAAQ,IAAI;AAAA,MAChB,iFACE,OACA;AAAA,IACJ;AACA,UAAM,OAAO;AACb,UAAM;AAAA,EACR;AAEA,MAAI,KAAK,aAAa,SAAS;AAE7B,UAAM,QAAQ,IAAI,UAAU,gCAAgC;AAC5D,UAAM,OAAO;AACb,UAAM;AAAA,EACR;AAEA,SAAO,oBAAoB,IAAI;AACjC;AAUA,SAAS,oBAAoB,KAAK;AAChC,MAAI,IAAI,aAAa,IAAI;AAEvB,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM,OAAO;AACb,UAAM;AAAA,EACR;AAEA,QAAM,WAAW,IAAI;AACrB,MAAIC,SAAQ;AAEZ,SAAO,EAAEA,SAAQ,SAAS,QAAQ;AAChC,QACE,SAAS,YAAYA,MAAK,MAAM,MAChC,SAAS,YAAYA,SAAQ,CAAC,MAAM,IACpC;AACA,YAAM,QAAQ,SAAS,YAAYA,SAAQ,CAAC;AAC5C,UAAI,UAAU,MAAgB,UAAU,KAAe;AAErD,cAAM,QAAQ,IAAI;AAAA,UAChB;AAAA,QACF;AACA,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO,mBAAmB,QAAQ;AACpC;;;ACvDA,IAAM;AAAA;AAAA,EAA8B;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAEO,IAAM,QAAN,MAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBjB,YAAY,OAAO;AAEjB,QAAI;AAEJ,QAAI,CAAC,OAAO;AACV,gBAAU,CAAC;AAAA,IACb,WAAW,MAAM,KAAK,GAAG;AACvB,gBAAU,EAAC,MAAM,MAAK;AAAA,IACxB,WAAW,OAAO,UAAU,YAAY,aAAa,KAAK,GAAG;AAC3D,gBAAU,EAAC,MAAK;AAAA,IAClB,OAAO;AACL,gBAAU;AAAA,IACZ;AAWA,SAAK,MAAM,SAAS,UAAU,KAAK,QAAQ,IAAI;AAU/C,SAAK,OAAO,CAAC;AASb,SAAK,UAAU,CAAC;AAOhB,SAAK,WAAW,CAAC;AAOjB,SAAK;AAYL,SAAK;AAUL,SAAK;AASL,SAAK;AAIL,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,MAAM,QAAQ;AAC7B,YAAMC,SAAQ,MAAMD,MAAK;AAIzB,UACEC,UAAS,WACT,QAAQA,MAAK,MAAM,UACnB,QAAQA,MAAK,MAAM,MACnB;AAEA,aAAKA,MAAK,IAAIA,WAAU,YAAY,CAAC,GAAG,QAAQA,MAAK,CAAC,IAAI,QAAQA,MAAK;AAAA,MACzE;AAAA,IACF;AAGA,QAAI;AAGJ,SAAK,SAAS,SAAS;AAErB,UAAI,CAAC,MAAM,SAAS,KAAK,GAAG;AAE1B,aAAK,KAAK,IAAI,QAAQ,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,WAAW;AACb,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,SAAS,KAAK,IAAI,IAC1B;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,SAASC,WAAU;AACrB,mBAAeA,WAAU,UAAU;AACnC,eAAWA,WAAU,UAAU;AAC/B,SAAK,OAAO,QAAQ,KAAK,KAAK,WAAW,IAAIA,SAAQ;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,UAAU;AACZ,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,QAAQ,KAAK,IAAI,IACzB;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,IAAI,QAAQC,UAAS;AACnB,IAAAC,YAAW,KAAK,UAAU,SAAS;AACnC,SAAK,OAAO,QAAQ,KAAKD,YAAW,IAAI,KAAK,QAAQ;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,UAAU;AACZ,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,QAAQ,KAAK,IAAI,IACzB;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,QAAQE,UAAS;AACnB,eAAWA,UAAS,SAAS;AAC7B,IAAAD,YAAW,KAAK,SAAS,SAAS;AAElC,QAAIC,UAAS;AACX,UAAIA,SAAQ,YAAY,CAAC,MAAM,IAAc;AAC3C,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AAEA,UAAIA,SAAQ,SAAS,KAAK,CAAC,GAAG;AAC5B,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC1D;AAAA,IACF;AAEA,SAAK,OAAO,QAAQ,KAAK,KAAK,SAAS,KAAK,QAAQA,YAAW,GAAG;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,KAAK,MAAM;AACb,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,UAAU,IAAI;AAAA,IACvB;AAEA,mBAAe,MAAM,MAAM;AAE3B,QAAI,KAAK,SAAS,MAAM;AACtB,WAAK,QAAQ,KAAK,IAAI;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,SAAS,KAAK,MAAM,KAAK,OAAO,IACxC;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,KAAK,MAAM;AACb,mBAAe,MAAM,MAAM;AAC3B,eAAW,MAAM,MAAM;AACvB,SAAK,OAAO,QAAQ,KAAK,KAAK,WAAW,IAAI,QAAQ,KAAK,WAAW,GAAG;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+DA,KAAK,eAAe,wBAAwB,QAAQ;AAElD,UAAM,UAAU,KAAK,QAAQ,eAAe,wBAAwB,MAAM;AAE1E,YAAQ,QAAQ;AAEhB,UAAM;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4DA,KAAK,eAAe,wBAAwB,QAAQ;AAElD,UAAM,UAAU,KAAK,QAAQ,eAAe,wBAAwB,MAAM;AAE1E,YAAQ,QAAQ;AAEhB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4DA,QAAQ,eAAe,wBAAwB,QAAQ;AACrD,UAAM,UAAU,IAAI;AAAA;AAAA,MAElB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,KAAK,MAAM;AACb,cAAQ,OAAO,KAAK,OAAO,MAAM,QAAQ;AACzC,cAAQ,OAAO,KAAK;AAAA,IACtB;AAEA,YAAQ,QAAQ;AAEhB,SAAK,SAAS,KAAK,OAAO;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,SAAS,UAAU;AACjB,QAAI,KAAK,UAAU,QAAW;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,UAAU,UAAU;AAClC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,IAAI,YAAY,YAAY,MAAS;AACrD,WAAO,QAAQ,OAAO,KAAK,KAAK;AAAA,EAClC;AACF;AAYA,SAAS,WAAW,MAAM,MAAM;AAC9B,MAAI,QAAQ,KAAK,SAAS,QAAQ,GAAG,GAAG;AACtC,UAAM,IAAI;AAAA,MACR,MAAM,OAAO,yCAAyC,QAAQ,MAAM;AAAA,IACtE;AAAA,EACF;AACF;AAYA,SAAS,eAAe,MAAM,MAAM;AAClC,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,MAAM,OAAO,mBAAmB;AAAA,EAClD;AACF;AAYA,SAASD,YAAW,MAAM,MAAM;AAC9B,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,cAAc,OAAO,iCAAiC;AAAA,EACxE;AACF;AAUA,SAAS,aAAa,OAAO;AAC3B,SAAO;AAAA,IACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAAA,EACpB;AACF;;;ACloBO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYP,SAAU,UAAU;AAClB,UAAM,OAAO;AACb,UAAM,SAAS,KAAK;AACpB,UAAM;AAAA;AAAA;AAAA;AAAA,MAGJ,OAAO;AAAA;AAET,UAAM,QAAQ,MAAM,QAAQ;AAE5B,UAAM,QAAQ,WAAY;AACxB,aAAO,MAAM,MAAM,OAAO,SAAS;AAAA,IACrC;AAEA,WAAO,eAAe,OAAO,KAAK;AAclC,WAAO;AAAA,EACT;AAAA;;;AViUN,IAAM,MAAM,CAAC,EAAE;AAeR,IAAM,YAAN,MAAM,mBAAkB,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAI9C,cAAc;AAEZ,UAAM,MAAM;AAeZ,SAAK,WAAW;AAYhB,SAAK,SAAS;AAad,SAAK,YAAY,CAAC;AAalB,SAAK,WAAW;AAShB,SAAK,cAAc;AASnB,SAAK,SAAS;AASd,SAAK,YAAY,CAAC;AAUlB,SAAK,SAAS;AASd,SAAK,eAAe,OAAO;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO;AAEL,UAAM;AAAA;AAAA,MAEF,IAAI,WAAU;AAAA;AAElB,QAAIE,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,KAAK,UAAU,QAAQ;AACtC,YAAM,WAAW,KAAK,UAAUA,MAAK;AACrC,kBAAY,IAAI,GAAG,QAAQ;AAAA,IAC7B;AAEA,gBAAY,SAAK,cAAAC,SAAO,MAAM,CAAC,GAAG,KAAK,SAAS,CAAC;AAEjD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6DA,KAAK,KAAK,OAAO;AACf,QAAI,OAAO,QAAQ,UAAU;AAE3B,UAAI,UAAU,WAAW,GAAG;AAC1B,uBAAe,QAAQ,KAAK,MAAM;AAClC,aAAK,UAAU,GAAG,IAAI;AACtB,eAAO;AAAA,MACT;AAGA,aAAQ,IAAI,KAAK,KAAK,WAAW,GAAG,KAAK,KAAK,UAAU,GAAG,KAAM;AAAA,IACnE;AAGA,QAAI,KAAK;AACP,qBAAe,QAAQ,KAAK,MAAM;AAClC,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAGA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,SAAS;AACP,QAAI,KAAK,QAAQ;AACf,aAAO;AAAA,IACT;AAKA,UAAM;AAAA;AAAA;AAAA,MAAyD;AAAA;AAE/D,WAAO,EAAE,KAAK,cAAc,KAAK,UAAU,QAAQ;AACjD,YAAM,CAAC,UAAU,GAAG,OAAO,IAAI,KAAK,UAAU,KAAK,WAAW;AAE9D,UAAI,QAAQ,CAAC,MAAM,OAAO;AACxB;AAAA,MACF;AAEA,UAAI,QAAQ,CAAC,MAAM,MAAM;AACvB,gBAAQ,CAAC,IAAI;AAAA,MACf;AAEA,YAAM,cAAc,SAAS,KAAK,MAAM,GAAG,OAAO;AAElD,UAAI,OAAO,gBAAgB,YAAY;AACrC,aAAK,aAAa,IAAI,WAAW;AAAA,MACnC;AAAA,IACF;AAEA,SAAK,SAAS;AACd,SAAK,cAAc,OAAO;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,MAAM;AACV,SAAK,OAAO;AACZ,UAAM,WAAW,MAAM,IAAI;AAC3B,UAAM,SAAS,KAAK,UAAU,KAAK;AACnC,iBAAa,SAAS,MAAM;AAC5B,WAAO,OAAO,OAAO,QAAQ,GAAG,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4CA,QAAQ,MAAM,MAAM;AAClB,UAAM,OAAO;AAEb,SAAK,OAAO;AACZ,iBAAa,WAAW,KAAK,UAAU,KAAK,MAAM;AAClD,mBAAe,WAAW,KAAK,YAAY,KAAK,QAAQ;AAExD,WAAO,OAAO,SAAS,QAAW,IAAI,IAAI,IAAI,QAAQ,QAAQ;AAQ9D,aAAS,SAAS,SAAS,QAAQ;AACjC,YAAM,WAAW,MAAM,IAAI;AAG3B,YAAM;AAAA;AAAA;AAAA,QAEsB,KAAK,MAAM,QAAQ;AAAA;AAG/C,WAAK,IAAI,WAAW,UAAU,SAAU,OAAO,MAAMC,OAAM;AACzD,YAAI,SAAS,CAAC,QAAQ,CAACA,OAAM;AAC3B,iBAAO,SAAS,KAAK;AAAA,QACvB;AAIA,cAAM;AAAA;AAAA;AAAA,UAEsB;AAAA;AAG5B,cAAM,gBAAgB,KAAK,UAAU,aAAaA,KAAI;AAEtD,YAAI,gBAAgB,aAAa,GAAG;AAClC,UAAAA,MAAK,QAAQ;AAAA,QACf,OAAO;AACL,UAAAA,MAAK,SAAS;AAAA,QAChB;AAEA;AAAA,UAAS;AAAA;AAAA,UAAsDA;AAAA,QAAK;AAAA,MACtE,CAAC;AAOD,eAAS,SAAS,OAAOA,OAAM;AAC7B,YAAI,SAAS,CAACA,OAAM;AAClB,iBAAO,KAAK;AAAA,QACd,WAAW,SAAS;AAClB,kBAAQA,KAAI;AAAA,QACd,OAAO;AACL,aAAO,MAAM,uCAAuC;AACpD,eAAK,QAAWA,KAAI;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCA,YAAY,MAAM;AAEhB,QAAI,WAAW;AAEf,QAAI;AAEJ,SAAK,OAAO;AACZ,iBAAa,eAAe,KAAK,UAAU,KAAK,MAAM;AACtD,mBAAe,eAAe,KAAK,YAAY,KAAK,QAAQ;AAE5D,SAAK,QAAQ,MAAM,QAAQ;AAC3B,eAAW,eAAe,WAAW,QAAQ;AAC7C,OAAO,QAAQ,6CAA6C;AAE5D,WAAO;AAKP,aAAS,SAAS,OAAOA,OAAM;AAC7B,iBAAW;AACX,WAAK,KAAK;AACV,eAASA;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwCA,IAAI,MAAM,MAAM,MAAM;AACpB,eAAW,IAAI;AACf,SAAK,OAAO;AAEZ,UAAM,eAAe,KAAK;AAE1B,QAAI,CAAC,QAAQ,OAAO,SAAS,YAAY;AACvC,aAAO;AACP,aAAO;AAAA,IACT;AAEA,WAAO,OAAO,SAAS,QAAW,IAAI,IAAI,IAAI,QAAQ,QAAQ;AAW9D,aAAS,SAAS,SAAS,QAAQ;AACjC;AAAA,QACE,OAAO,SAAS;AAAA,QAChB;AAAA,MACF;AACA,YAAM,WAAW,MAAM,IAAI;AAC3B,mBAAa,IAAI,MAAM,UAAU,QAAQ;AAQzC,eAAS,SAAS,OAAO,YAAYA,OAAM;AACzC,cAAM;AAAA;AAAA,UAEF,cAAc;AAAA;AAGlB,YAAI,OAAO;AACT,iBAAO,KAAK;AAAA,QACd,WAAW,SAAS;AAClB,kBAAQ,aAAa;AAAA,QACvB,OAAO;AACL,aAAO,MAAM,uCAAuC;AACpD,eAAK,QAAW,eAAeA,KAAI;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,QAAQ,MAAM,MAAM;AAElB,QAAI,WAAW;AAEf,QAAI;AAEJ,SAAK,IAAI,MAAM,MAAM,QAAQ;AAE7B,eAAW,WAAW,OAAO,QAAQ;AACrC,OAAO,QAAQ,6CAA6C;AAC5D,WAAO;AAKP,aAAS,SAAS,OAAOC,OAAM;AAC7B,WAAK,KAAK;AACV,eAASA;AACT,iBAAW;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,UAAU,MAAM,MAAM;AACpB,SAAK,OAAO;AACZ,UAAM,WAAW,MAAM,IAAI;AAC3B,UAAM,WAAW,KAAK,YAAY,KAAK;AACvC,mBAAe,aAAa,QAAQ;AACpC,eAAW,IAAI;AAEf,WAAO,SAAS,MAAM,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2DA,IAAI,UAAU,YAAY;AACxB,UAAM,YAAY,KAAK;AACvB,UAAM,YAAY,KAAK;AAEvB,mBAAe,OAAO,KAAK,MAAM;AAEjC,QAAI,UAAU,QAAQ,UAAU,QAAW;AAAA,IAE3C,WAAW,OAAO,UAAU,YAAY;AACtC,gBAAU,OAAO,UAAU;AAAA,IAC7B,WAAW,OAAO,UAAU,UAAU;AACpC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAQ,KAAK;AAAA,MACf,OAAO;AACL,kBAAU,KAAK;AAAA,MACjB;AAAA,IACF,OAAO;AACL,YAAM,IAAI,UAAU,iCAAiC,QAAQ,GAAG;AAAA,IAClE;AAEA,WAAO;AAMP,aAAS,IAAIC,QAAO;AAClB,UAAI,OAAOA,WAAU,YAAY;AAC/B,kBAAUA,QAAO,CAAC,CAAC;AAAA,MACrB,WAAW,OAAOA,WAAU,UAAU;AACpC,YAAI,MAAM,QAAQA,MAAK,GAAG;AACxB,gBAAM,CAAC,QAAQ,GAAGC,WAAU;AAAA;AAAA,YACkBD;AAAA;AAC9C,oBAAU,QAAQC,WAAU;AAAA,QAC9B,OAAO;AACL,oBAAUD,MAAK;AAAA,QACjB;AAAA,MACF,OAAO;AACL,cAAM,IAAI,UAAU,iCAAiCA,SAAQ,GAAG;AAAA,MAClE;AAAA,IACF;AAMA,aAAS,UAAU,QAAQ;AACzB,UAAI,EAAE,aAAa,WAAW,EAAE,cAAc,SAAS;AACrD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,OAAO,OAAO;AAEtB,UAAI,OAAO,UAAU;AACnB,kBAAU,eAAW,cAAAH,SAAO,MAAM,UAAU,UAAU,OAAO,QAAQ;AAAA,MACvE;AAAA,IACF;AAMA,aAAS,QAAQ,SAAS;AACxB,UAAID,SAAQ;AAEZ,UAAI,YAAY,QAAQ,YAAY,QAAW;AAAA,MAE/C,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,eAAO,EAAEA,SAAQ,QAAQ,QAAQ;AAC/B,gBAAM,QAAQ,QAAQA,MAAK;AAC3B,cAAI,KAAK;AAAA,QACX;AAAA,MACF,OAAO;AACL,cAAM,IAAI,UAAU,sCAAsC,UAAU,GAAG;AAAA,MACzE;AAAA,IACF;AAOA,aAAS,UAAU,QAAQK,aAAY;AACrC,UAAIL,SAAQ;AACZ,UAAI,aAAa;AAEjB,aAAO,EAAEA,SAAQ,UAAU,QAAQ;AACjC,YAAI,UAAUA,MAAK,EAAE,CAAC,MAAM,QAAQ;AAClC,uBAAaA;AACb;AAAA,QACF;AAAA,MACF;AAEA,UAAI,eAAe,IAAI;AACrB,kBAAU,KAAK,CAAC,QAAQ,GAAGK,WAAU,CAAC;AAAA,MACxC,WAGSA,YAAW,SAAS,GAAG;AAC9B,YAAI,CAAC,SAAS,GAAG,IAAI,IAAIA;AACzB,cAAM,iBAAiB,UAAU,UAAU,EAAE,CAAC;AAC9C,YAAI,cAAW,cAAc,KAAK,cAAW,OAAO,GAAG;AACrD,wBAAU,cAAAJ,SAAO,MAAM,gBAAgB,OAAO;AAAA,QAChD;AAEA,kBAAU,UAAU,IAAI,CAAC,QAAQ,SAAS,GAAG,IAAI;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACF;AA8BO,IAAM,UAAU,IAAI,UAAU,EAAE,OAAO;AAS9C,SAAS,aAAa,MAAM,OAAO;AACjC,MAAI,OAAO,UAAU,YAAY;AAC/B,UAAM,IAAI,UAAU,aAAa,OAAO,oBAAoB;AAAA,EAC9D;AACF;AASA,SAAS,eAAe,MAAM,OAAO;AACnC,MAAI,OAAO,UAAU,YAAY;AAC/B,UAAM,IAAI,UAAU,aAAa,OAAO,sBAAsB;AAAA,EAChE;AACF;AASA,SAAS,eAAe,MAAM,QAAQ;AACpC,MAAI,QAAQ;AACV,UAAM,IAAI;AAAA,MACR,kBACE,OACA;AAAA,IACJ;AAAA,EACF;AACF;AAQA,SAAS,WAAW,MAAM;AAGxB,MAAI,CAAC,cAAW,IAAI,KAAK,OAAO,KAAK,SAAS,UAAU;AACtD,UAAM,IAAI,UAAU,yBAAyB,OAAO,GAAG;AAAA,EAEzD;AACF;AAUA,SAAS,WAAW,MAAM,WAAW,UAAU;AAC7C,MAAI,CAAC,UAAU;AACb,UAAM,IAAI;AAAA,MACR,MAAM,OAAO,4BAA4B,YAAY;AAAA,IACvD;AAAA,EACF;AACF;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,gBAAgB,KAAK,IAAI,QAAQ,IAAI,MAAM,KAAK;AACzD;AAMA,SAAS,gBAAgB,OAAO;AAC9B,SAAO;AAAA,IACL,SACE,OAAO,UAAU,YACjB,aAAa,SACb,cAAc;AAAA,EAClB;AACF;AAMA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,OAAO,UAAU,YAAYK,cAAa,KAAK;AACxD;AAUA,SAASA,cAAa,OAAO;AAC3B,SAAO;AAAA,IACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAAA,EACpB;AACF;", "names": ["isArray", "isPlainObject", "setProperty", "getProperty", "extend", "index", "point", "index", "extname", "index", "index", "index", "field", "basename", "dirname", "assertPath", "extname", "index", "extend", "file", "tree", "value", "parameters", "isUint8Array"]}