<template>
  <div class="relative">
    <button
      class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-slate-300 hover:text-white hover:bg-slate-800 transition-colors duration-200 focus-ring"
      @click="toggleDropdown"
    >
      <Icon name="heroicons:language" class="w-4 h-4" />
      <span>{{ currentLocale.name }}</span>
      <Icon
        name="heroicons:chevron-down"
        class="w-4 h-4 transition-transform duration-200"
        :class="{ 'rotate-180': isOpen }"
      />
    </button>

    <!-- 下拉菜单 -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="isOpen"
        class="absolute right-0 mt-2 w-48 glass rounded-lg shadow-lg border border-white/10 z-50"
      >
        <div class="py-2">
          <button
            v-for="locale in availableLocales"
            :key="locale.code"
            class="w-full flex items-center px-4 py-2 text-sm text-slate-300 hover:text-white hover:bg-slate-800 transition-colors duration-200"
            :class="{ 'text-white bg-slate-800': locale.code === currentLocale.code }"
            @click="switchLanguage(locale.code)"
          >
            <span class="mr-3 text-lg">{{ locale.flag }}</span>
            <span>{{ locale.name }}</span>
            <Icon
              v-if="locale.code === currentLocale.code"
              name="heroicons:check"
              class="w-4 h-4 ml-auto text-green-400"
            />
          </button>
        </div>
      </div>
    </Transition>

    <!-- 点击外部关闭 -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup lang="ts">
const { locale, locales, setLocale } = useI18n()
const localePath = useLocalePath()

const isOpen = ref(false)

// 可用语言列表
const availableLocales = computed(() => [
  { code: 'zh-CN', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
])

// 当前语言
const currentLocale = computed(() => {
  return availableLocales.value.find(l => l.code === locale.value) || availableLocales.value[0]
})

// 切换下拉菜单
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// 关闭下拉菜单
const closeDropdown = () => {
  isOpen.value = false
}

// 切换语言
const switchLanguage = async (newLocale: string) => {
  if (newLocale !== locale.value) {
    await setLocale(newLocale)
    
    // 保存语言偏好到 localStorage
    if (process.client) {
      localStorage.setItem('preferred-language', newLocale)
    }
  }
  closeDropdown()
}

// 监听键盘事件
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen.value) {
      closeDropdown()
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>
