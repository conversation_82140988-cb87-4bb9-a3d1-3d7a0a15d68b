import e from"node:process";globalThis._importMeta_={url:import.meta.url,env:e.env};import{tmpdir as t}from"node:os";import{Server as n}from"node:http";import{resolve as o,dirname as r,join as s}from"node:path";import i from"node:crypto";import{parentPort as a,threadId as c}from"node:worker_threads";import{defineEventHandler as l,handleCacheHeaders as u,splitCookiesString as d,createEvent as f,fetchWithEvent as p,isEvent as h,eventHandler as m,setHeaders as g,sendRedirect as y,proxyRequest as w,getRequestHeader as b,setResponseHeaders as x,setResponseStatus as v,send as _,getRequestHeaders as R,setResponseHeader as E,getRequestURL as k,getResponseHeader as S,getResponseStatus as j,createError as A,getQuery as C,readBody as O,getRouterParam as T,setHeader as L,createApp as $,createRouter as U,toNodeL<PERSON>ener as N,lazy<PERSON>vent<PERSON>and<PERSON> as I,getResponseStatusText as W}from"file://E:/WORK/test5/next-novel-website/node_modules/h3/dist/index.mjs";import{escapeHtml as H}from"file://E:/WORK/test5/next-novel-website/node_modules/@vue/shared/dist/shared.cjs.js";import{createRenderer as P,getRequestDependencies as K,getPreloadLinks as z,getPrefetchLinks as M}from"file://E:/WORK/test5/next-novel-website/node_modules/vue-bundle-renderer/dist/runtime.mjs";import{renderToString as D}from"file://E:/WORK/test5/next-novel-website/node_modules/vue/server-renderer/index.mjs";import{klona as F}from"file://E:/WORK/test5/next-novel-website/node_modules/klona/dist/index.mjs";import q,{defuFn as B}from"file://E:/WORK/test5/next-novel-website/node_modules/defu/dist/defu.mjs";import Q,{destr as V}from"file://E:/WORK/test5/next-novel-website/node_modules/destr/dist/index.mjs";import{snakeCase as J}from"file://E:/WORK/test5/next-novel-website/node_modules/scule/dist/index.mjs";import{createHead as X,propsToString as Y,renderSSRHead as G}from"file://E:/WORK/test5/next-novel-website/node_modules/unhead/dist/server.mjs";import{stringify as Z,uneval as ee}from"file://E:/WORK/test5/next-novel-website/node_modules/devalue/index.js";import{isVNode as te,toValue as ne,isRef as oe}from"file://E:/WORK/test5/next-novel-website/node_modules/vue/index.mjs";import{DeprecationsPlugin as re,PromisesPlugin as se,TemplateParamsPlugin as ie,AliasSortingPlugin as ae}from"file://E:/WORK/test5/next-novel-website/node_modules/unhead/dist/plugins.mjs";import{createHooks as ce}from"file://E:/WORK/test5/next-novel-website/node_modules/hookable/dist/index.mjs";import{createFetch as le,Headers as ue}from"file://E:/WORK/test5/next-novel-website/node_modules/ofetch/dist/node.mjs";import{fetchNodeRequestHandler as de,callNodeRequestHandler as fe}from"file://E:/WORK/test5/next-novel-website/node_modules/node-mock-http/dist/index.mjs";import{createStorage as pe,prefixStorage as he}from"file://E:/WORK/test5/next-novel-website/node_modules/unstorage/dist/index.mjs";import me from"file://E:/WORK/test5/next-novel-website/node_modules/unstorage/drivers/fs.mjs";import{digest as ge}from"file://E:/WORK/test5/next-novel-website/node_modules/ohash/dist/index.mjs";import{toRouteMatcher as ye,createRouter as we}from"file://E:/WORK/test5/next-novel-website/node_modules/radix3/dist/index.mjs";import{readFile as be}from"node:fs/promises";import xe,{consola as ve}from"file://E:/WORK/test5/next-novel-website/node_modules/consola/dist/index.mjs";import{ErrorParser as _e}from"file://E:/WORK/test5/next-novel-website/node_modules/youch-core/build/index.js";import{Youch as Re}from"file://E:/WORK/test5/next-novel-website/node_modules/youch/build/index.js";import{SourceMapConsumer as Ee}from"file://E:/WORK/test5/next-novel-website/node_modules/source-map/source-map.js";import{AsyncLocalStorage as ke}from"node:async_hooks";import{getContext as Se}from"file://E:/WORK/test5/next-novel-website/node_modules/unctx/dist/index.mjs";import{captureRawStackTrace as je,parseRawStackTrace as Ae}from"file://E:/WORK/test5/next-novel-website/node_modules/errx/dist/index.js";import{basename as Ce,isAbsolute as Oe}from"file://E:/WORK/test5/node_modules/pathe/dist/index.mjs";import{getIcons as Te}from"file://E:/WORK/test5/node_modules/@iconify/utils/lib/index.mjs";import{hash as Le}from"file://E:/WORK/test5/node_modules/ohash/dist/index.mjs";import{getQuery as $e,createError as Ue,lazyEventHandler as Ne,useBase as Ie}from"file://E:/WORK/test5/node_modules/h3/dist/index.mjs";import{consola as We}from"file://E:/WORK/test5/node_modules/consola/dist/index.mjs";import{collections as He}from"file://E:/WORK/test5/next-novel-website/.nuxt/nuxt-icon-server-bundle.mjs";import{walkResolver as Pe}from"file://E:/WORK/test5/next-novel-website/node_modules/unhead/dist/utils.mjs";import{isAbsolute as Ke}from"file://E:/WORK/test5/next-novel-website/node_modules/pathe/dist/index.mjs";import ze from"file://E:/WORK/test5/next-novel-website/node_modules/db0/dist/connectors/better-sqlite3.mjs";import{fileURLToPath as Me}from"node:url";import{ipxFSStorage as De,ipxHttpStorage as Fe,createIPX as qe,createIPXH3Handler as Be}from"file://E:/WORK/test5/node_modules/ipx/dist/index.mjs";const Qe=/#/g,Ve=/&/g,Je=/\//g,Xe=/=/g,Ye=/\+/g,Ge=/%5e/gi,Ze=/%60/gi,et=/%7c/gi,tt=/%20/gi;function encodeQueryValue(e){return(t="string"==typeof e?e:JSON.stringify(e),encodeURI(""+t).replace(et,"|")).replace(Ye,"%2B").replace(tt,"+").replace(Qe,"%23").replace(Ve,"%26").replace(Ze,"`").replace(Ge,"^").replace(Je,"%2F");var t}function encodeQueryKey(e){return encodeQueryValue(e).replace(Xe,"%3D")}function decode(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function decodeQueryKey(e){return decode(e.replace(Ye," "))}function decodeQueryValue(e){return decode(e.replace(Ye," "))}function parseQuery(e=""){const t=Object.create(null);"?"===e[0]&&(e=e.slice(1));for(const n of e.split("&")){const e=n.match(/([^=]+)=?(.*)/)||[];if(e.length<2)continue;const o=decodeQueryKey(e[1]);if("__proto__"===o||"constructor"===o)continue;const r=decodeQueryValue(e[2]||"");void 0===t[o]?t[o]=r:Array.isArray(t[o])?t[o].push(r):t[o]=[t[o],r]}return t}function stringifyQuery(e){return Object.keys(e).filter(t=>void 0!==e[t]).map(t=>{return n=t,"number"!=typeof(o=e[t])&&"boolean"!=typeof o||(o=String(o)),o?Array.isArray(o)?o.map(e=>`${encodeQueryKey(n)}=${encodeQueryValue(e)}`).join("&"):`${encodeQueryKey(n)}=${encodeQueryValue(o)}`:encodeQueryKey(n);var n,o}).filter(Boolean).join("&")}const nt=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,ot=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,rt=/^([/\\]\s*){2,}[^/\\]/,st=/^\.?\//;function hasProtocol(e,t={}){return"boolean"==typeof t&&(t={acceptRelative:t}),t.strict?nt.test(e):ot.test(e)||!!t.acceptRelative&&rt.test(e)}function withTrailingSlash(e="",t){return e.endsWith("/")?e:e+"/"}function withoutBase(e,t){if(!(n=t)||"/"===n)return e;var n;const o=function(e=""){return(function(e=""){return e.endsWith("/")}(e)?e.slice(0,-1):e)||"/"}(t);if(!e.startsWith(o))return e;const r=e.slice(o.length);return"/"===r[0]?r:"/"+r}function withQuery(e,t){const n=parseURL(e),o={...parseQuery(n.search),...t};return n.search=stringifyQuery(o),function(e){const t=e.pathname||"",n=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",o=e.hash||"",r=e.auth?e.auth+"@":"",s=e.host||"",i=e.protocol||e[it]?(e.protocol||"")+"//":"";return i+r+s+t+n+o}(n)}function getQuery(e){return parseQuery(parseURL(e).search)}function joinURL(e,...t){let n=e||"";for(const e of t.filter(e=>function(e){return e&&"/"!==e}(e)))if(n){const t=e.replace(st,"");n=withTrailingSlash(n)+t}else n=e;return n}function joinRelativeURL(...e){const t=/\/(?!\/)/,n=e.filter(Boolean),o=[];let r=0;for(const e of n)if(e&&"/"!==e)for(const[n,s]of e.split(t).entries())if(s&&"."!==s)if(".."!==s)1===n&&o[o.length-1]?.endsWith(":/")?o[o.length-1]+="/"+s:(o.push(s),r++);else{if(1===o.length&&hasProtocol(o[0]))continue;o.pop(),r--}let s=o.join("/");return r>=0?n[0]?.startsWith("/")&&!s.startsWith("/")?s="/"+s:n[0]?.startsWith("./")&&!s.startsWith("./")&&(s="./"+s):s="../".repeat(-1*r)+s,n[n.length-1]?.endsWith("/")&&!s.endsWith("/")&&(s+="/"),s}const it=Symbol.for("ufo:protocolRelative");function parseURL(e="",t){const n=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(n){const[,e,t=""]=n;return{protocol:e.toLowerCase(),pathname:t,href:e+t,auth:"",host:"",search:"",hash:""}}if(!hasProtocol(e,{acceptRelative:!0}))return parsePath(e);const[,o="",r,s=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,i="",a=""]=s.match(/([^#/?]*)(.*)?/)||[];"file:"===o&&(a=a.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:c,search:l,hash:u}=parsePath(a);return{protocol:o.toLowerCase(),auth:r?r.slice(0,Math.max(0,r.length-1)):"",host:i,pathname:c,search:l,hash:u,[it]:!o}}function parsePath(e=""){const[t="",n="",o=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:n,hash:o}}const at=[{baseName:"server",dir:"E:/WORK/test5/next-novel-website/server/assets"}],ct=pe();for(const e of at)ct.mount(e.baseName,me({base:e.dir,ignore:e?.ignore||[]}));const lt=pe({});function useStorage(e=""){return e?he(lt,e):lt}lt.mount("/assets",ct),lt.mount("root",me({driver:"fs",readOnly:!0,base:"E:/WORK/test5/next-novel-website",watchOptions:{ignored:[null]}})),lt.mount("src",me({driver:"fs",readOnly:!0,base:"E:/WORK/test5/next-novel-website/server",watchOptions:{ignored:[null]}})),lt.mount("build",me({driver:"fs",readOnly:!1,base:"E:/WORK/test5/next-novel-website/.nuxt"})),lt.mount("cache",me({driver:"fs",readOnly:!1,base:"E:/WORK/test5/next-novel-website/.nuxt/cache"})),lt.mount("data",me({driver:"fs",base:"E:/WORK/test5/next-novel-website/.data/kv"}));const ut=(()=>{class Hasher2{buff="";#e=new Map;write(e){this.buff+=e}dispatch(e){return this[null===e?"null":typeof e](e)}object(e){if(e&&"function"==typeof e.toJSON)return this.object(e.toJSON());const t=Object.prototype.toString.call(e);let n="";const o=t.length;n=o<10?"unknown:["+t+"]":t.slice(8,o-1),n=n.toLowerCase();let r=null;if(void 0!==(r=this.#e.get(e)))return this.dispatch("[CIRCULAR:"+r+"]");if(this.#e.set(e,this.#e.size),"undefined"!=typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(e))return this.write("buffer:"),this.write(e.toString("utf8"));if("object"!==n&&"function"!==n&&"asyncfunction"!==n)this[n]?this[n](e):this.unknown(e,n);else{const t=Object.keys(e).sort(),n=[];this.write("object:"+(t.length+n.length)+":");const dispatchForKey=t=>{this.dispatch(t),this.write(":"),this.dispatch(e[t]),this.write(",")};for(const e of t)dispatchForKey(e);for(const e of n)dispatchForKey(e)}}array(e,t){if(t=void 0!==t&&t,this.write("array:"+e.length+":"),!t||e.length<=1){for(const t of e)this.dispatch(t);return}const n=new Map,o=e.map(e=>{const t=new Hasher2;t.dispatch(e);for(const[e,o]of t.#e)n.set(e,o);return t.toString()});return this.#e=n,o.sort(),this.array(o,!1)}date(e){return this.write("date:"+e.toJSON())}symbol(e){return this.write("symbol:"+e.toString())}unknown(e,t){if(this.write(t),e)return this.write(":"),e&&"function"==typeof e.entries?this.array([...e.entries()],!0):void 0}error(e){return this.write("error:"+e.toString())}boolean(e){return this.write("bool:"+e)}string(e){this.write("string:"+e.length+":"),this.write(e)}function(e){this.write("fn:"),!function(e){if("function"!=typeof e)return!1;return"[native code] }"===Function.prototype.toString.call(e).slice(-15)}(e)?this.dispatch(e.toString()):this.dispatch("[native]")}number(e){return this.write("number:"+e)}null(){return this.write("Null")}undefined(){return this.write("Undefined")}regexp(e){return this.write("regex:"+e.toString())}arraybuffer(e){return this.write("arraybuffer:"),this.dispatch(new Uint8Array(e))}url(e){return this.write("url:"+e.toString())}map(e){this.write("map:");const t=[...e];return this.array(t,!1)}set(e){this.write("set:");const t=[...e];return this.array(t,!1)}bigint(e){return this.write("bigint:"+e.toString())}}for(const e of["uint8array","uint8clampedarray","unt8array","uint16array","unt16array","uint32array","unt32array","float32array","float64array"])Hasher2.prototype[e]=function(t){return this.write(e+":"),this.array([...t],!1)};return Hasher2})();function hash(e){return ge("string"==typeof e?e:function(e){const t=new ut;return t.dispatch(e),t.buff}(e)).replace(/[-_]/g,"").slice(0,10)}function defineCachedFunction(e,t={}){t={name:"_",base:"/cache",swr:!0,maxAge:1,...t};const n={},o=t.group||"nitro/functions",r=t.name||e.name||"_",s=t.integrity||hash([e,t]),i=t.validate||(e=>void 0!==e.value);return async(...a)=>{if(await(t.shouldBypassCache?.(...a)))return e(...a);const c=await(t.getKey||getKey)(...a),l=await(t.shouldInvalidateCache?.(...a)),u=await async function(e,a,c,l){const u=[t.base,o,r,e+".json"].filter(Boolean).join(":").replace(/:\/$/,":index");let d=await useStorage().getItem(u).catch(e=>{console.error("[cache] Cache read error.",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})})||{};if("object"!=typeof d){d={};const e=new Error("Malformed data read from cache.");console.error("[cache]",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})}const f=1e3*(t.maxAge??0);f&&(d.expires=Date.now()+f);const p=c||d.integrity!==s||f&&Date.now()-(d.mtime||0)>f||!1===i(d),h=p?(async()=>{const o=n[e];o||(void 0!==d.value&&(t.staleMaxAge||0)>=0&&!1===t.swr&&(d.value=void 0,d.integrity=void 0,d.mtime=void 0,d.expires=void 0),n[e]=Promise.resolve(a()));try{d.value=await n[e]}catch(t){throw o||delete n[e],t}if(!o&&(d.mtime=Date.now(),d.integrity=s,delete n[e],!1!==i(d))){let e;t.maxAge&&!t.swr&&(e={ttl:t.maxAge});const n=useStorage().setItem(u,d,e).catch(e=>{console.error("[cache] Cache write error.",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})});l?.waitUntil&&l.waitUntil(n)}})():Promise.resolve();return void 0===d.value?await h:p&&l&&l.waitUntil&&l.waitUntil(h),t.swr&&!1!==i(d)?(h.catch(e=>{console.error("[cache] SWR handler error.",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})}),d):h.then(()=>d)}(c,()=>e(...a),l,a[0]&&h(a[0])?a[0]:void 0);let d=u.value;return t.transform&&(d=await t.transform(u,...a)||d),d}}function getKey(...e){return e.length>0?hash(e):""}function escapeKey(e){return String(e).replace(/\W/g,"")}function defineCachedEventHandler(e,t={name:"_",base:"/cache",swr:!0,maxAge:1}){const n=(t.varies||[]).filter(Boolean).map(e=>e.toLowerCase()).sort(),o={...t,getKey:async e=>{const o=await(t.getKey?.(e));if(o)return escapeKey(o);const r=e.node.req.originalUrl||e.node.req.url||e.path;let s;try{s=escapeKey(decodeURI(parseURL(r).pathname)).slice(0,16)||"index"}catch{s="-"}return[`${s}.${hash(r)}`,...n.map(t=>[t,e.node.req.headers[t]]).map(([e,t])=>`${escapeKey(e)}.${hash(t)}`)].join(":")},validate:e=>!!e.value&&(!(e.value.code>=400)&&(void 0!==e.value.body&&("undefined"!==e.value.headers.etag&&"undefined"!==e.value.headers["last-modified"]))),group:t.group||"nitro/handlers",integrity:t.integrity||hash([e,t])},r=function(e,t={}){return defineCachedFunction(e,t)}(async r=>{const s={};for(const e of n){const t=r.node.req.headers[e];void 0!==t&&(s[e]=t)}const i=cloneWithProxy(r.node.req,{headers:s}),a={};let c;const l=cloneWithProxy(r.node.res,{statusCode:200,writableEnded:!1,writableFinished:!1,headersSent:!1,closed:!1,getHeader:e=>a[e],setHeader(e,t){return a[e]=t,this},getHeaderNames:()=>Object.keys(a),hasHeader:e=>e in a,removeHeader(e){delete a[e]},getHeaders:()=>a,end(e,t,n){return"string"==typeof e&&(c=e),"function"==typeof t&&t(),"function"==typeof n&&n(),this},write:(e,t,n)=>("string"==typeof e&&(c=e),"function"==typeof t&&t(void 0),"function"==typeof n&&n(),!0),writeHead(e,t){if(this.statusCode=e,t){if(Array.isArray(t)||"string"==typeof t)throw new TypeError("Raw headers  is not supported.");for(const e in t){const n=t[e];void 0!==n&&this.setHeader(e,n)}}return this}}),u=f(i,l);u.fetch=(e,t)=>p(u,e,t,{fetch:useNitroApp().localFetch}),u.$fetch=(e,t)=>p(u,e,t,{fetch:globalThis.$fetch}),u.waitUntil=r.waitUntil,u.context=r.context,u.context.cache={options:o};const d=await e(u)||c,h=u.node.res.getHeaders();h.etag=String(h.Etag||h.etag||`W/"${hash(d)}"`),h["last-modified"]=String(h["Last-Modified"]||h["last-modified"]||(new Date).toUTCString());const m=[];t.swr?(t.maxAge&&m.push(`s-maxage=${t.maxAge}`),t.staleMaxAge?m.push(`stale-while-revalidate=${t.staleMaxAge}`):m.push("stale-while-revalidate")):t.maxAge&&m.push(`max-age=${t.maxAge}`),m.length>0&&(h["cache-control"]=m.join(", "));return{code:u.node.res.statusCode,headers:h,body:d}},o);return l(async n=>{if(t.headersOnly){if(u(n,{maxAge:t.maxAge}))return;return e(n)}const o=await r(n);if(n.node.res.headersSent||n.node.res.writableEnded)return o.body;if(!u(n,{modifiedTime:new Date(o.headers["last-modified"]),etag:o.headers.etag,maxAge:t.maxAge})){n.node.res.statusCode=o.code;for(const e in o.headers){const t=o.headers[e];"set-cookie"===e?n.node.res.appendHeader(e,d(t)):void 0!==t&&n.node.res.setHeader(e,t)}return o.body}})}function cloneWithProxy(e,t){return new Proxy(e,{get:(e,n,o)=>n in t?t[n]:Reflect.get(e,n,o),set:(e,n,o,r)=>n in t?(t[n]=o,!0):Reflect.set(e,n,o,r)})}const dt=defineCachedEventHandler,ft=B({nuxt:{},ui:{colors:{primary:"green",secondary:"blue",success:"green",info:"blue",warning:"yellow",error:"red",neutral:"slate"},icons:{arrowLeft:"i-lucide-arrow-left",arrowRight:"i-lucide-arrow-right",check:"i-lucide-check",chevronDoubleLeft:"i-lucide-chevrons-left",chevronDoubleRight:"i-lucide-chevrons-right",chevronDown:"i-lucide-chevron-down",chevronLeft:"i-lucide-chevron-left",chevronRight:"i-lucide-chevron-right",chevronUp:"i-lucide-chevron-up",close:"i-lucide-x",ellipsis:"i-lucide-ellipsis",external:"i-lucide-arrow-up-right",folder:"i-lucide-folder",folderOpen:"i-lucide-folder-open",loading:"i-lucide-loader-circle",minus:"i-lucide-minus",plus:"i-lucide-plus",search:"i-lucide-search"}},icon:{provider:"server",class:"",aliases:{},iconifyApiEndpoint:"https://api.iconify.design",localApiEndpoint:"/api/_nuxt_icon",fallbackToApi:!0,cssSelectorPrefix:"i-",cssWherePseudo:!0,cssLayer:"components",mode:"css",attrs:{"aria-hidden":!0},collections:["academicons","akar-icons","ant-design","arcticons","basil","bi","bitcoin-icons","bpmn","brandico","bx","bxl","bxs","bytesize","carbon","catppuccin","cbi","charm","ci","cib","cif","cil","circle-flags","circum","clarity","codicon","covid","cryptocurrency","cryptocurrency-color","dashicons","devicon","devicon-plain","ei","el","emojione","emojione-monotone","emojione-v1","entypo","entypo-social","eos-icons","ep","et","eva","f7","fa","fa-brands","fa-regular","fa-solid","fa6-brands","fa6-regular","fa6-solid","fad","fe","feather","file-icons","flag","flagpack","flat-color-icons","flat-ui","flowbite","fluent","fluent-emoji","fluent-emoji-flat","fluent-emoji-high-contrast","fluent-mdl2","fontelico","fontisto","formkit","foundation","fxemoji","gala","game-icons","geo","gg","gis","gravity-ui","gridicons","grommet-icons","guidance","healthicons","heroicons","heroicons-outline","heroicons-solid","hugeicons","humbleicons","ic","icomoon-free","icon-park","icon-park-outline","icon-park-solid","icon-park-twotone","iconamoon","iconoir","icons8","il","ion","iwwa","jam","la","lets-icons","line-md","logos","ls","lucide","lucide-lab","mage","majesticons","maki","map","marketeq","material-symbols","material-symbols-light","mdi","mdi-light","medical-icon","memory","meteocons","mi","mingcute","mono-icons","mynaui","nimbus","nonicons","noto","noto-v1","octicon","oi","ooui","openmoji","oui","pajamas","pepicons","pepicons-pencil","pepicons-pop","pepicons-print","ph","pixelarticons","prime","ps","quill","radix-icons","raphael","ri","rivet-icons","si-glyph","simple-icons","simple-line-icons","skill-icons","solar","streamline","streamline-emojis","subway","svg-spinners","system-uicons","tabler","tdesign","teenyicons","token","token-branded","topcoat","twemoji","typcn","uil","uim","uis","uit","uiw","unjs","vaadin","vs","vscode-icons","websymbol","weui","whh","wi","wpf","zmdi","zondicons"],fetchTimeout:1500}});function getEnv(t,n){const o=J(t).toUpperCase();return Q(e.env[n.prefix+o]??e.env[n.altPrefix+o])}function _isObject(e){return"object"==typeof e&&!Array.isArray(e)}function applyEnv(e,t,n=""){for(const o in e){const r=n?`${n}_${o}`:o,s=getEnv(r,t);_isObject(e[o])?_isObject(s)?(e[o]={...e[o],...s},applyEnv(e[o],t,r)):void 0===s?applyEnv(e[o],t,r):e[o]=s??e[o]:e[o]=s??e[o],t.envExpansion&&"string"==typeof e[o]&&(e[o]=_expandFromEnv(e[o]))}return e}const pt=/\{\{([^{}]*)\}\}/g;function _expandFromEnv(t){return t.replace(pt,(t,n)=>e.env[n]||t)}const ht={app:{baseURL:"/",buildId:"dev",buildAssetsDir:"/_nuxt/",cdnURL:""},nitro:{envPrefix:"NUXT_",routeRules:{"/__nuxt_error":{cache:!1},"/__nuxt_content/**":{robots:!1},"/__nuxt_content/content/sql_dump.txt":{prerender:!0},"/_fonts/**":{headers:{"cache-control":"public, max-age=31536000, immutable"},cache:{maxAge:31536e3}},"/_nuxt/builds/meta/**":{headers:{"cache-control":"public, max-age=31536000, immutable"}},"/_nuxt/builds/**":{headers:{"cache-control":"public, max-age=1, immutable"}}}},public:{apiBase:"/api",siteUrl:"http://localhost:3000",content:{wsUrl:"ws://localhost:4000/"},mdc:{components:{prose:!0,map:{}},headings:{anchorLinks:{h1:!1,h2:!0,h3:!0,h4:!0,h5:!1,h6:!1}}},i18n:{baseUrl:"",defaultLocale:"zh-CN",defaultDirection:"ltr",strategy:"prefix_except_default",lazy:!1,rootRedirect:"",routesNameSeparator:"___",defaultLocaleRouteNameSuffix:"default",skipSettingLocaleOnNavigate:!1,differentDomains:!1,trailingSlash:!1,locales:[{code:"zh-CN",name:"中文",files:[{path:"E:/WORK/test5/next-novel-website/i18n/locales/zh-CN.json",cache:""}]},{code:"en",name:"English",files:[{path:"E:/WORK/test5/next-novel-website/i18n/locales/en.json",cache:""}]}],detectBrowserLanguage:{alwaysRedirect:!1,cookieCrossOrigin:!1,cookieDomain:"",cookieKey:"i18n_redirected",cookieSecure:!1,fallbackLocale:"",redirectOn:"root",useCookie:!0},experimental:{localeDetector:"",switchLocalePathLinkSSR:!1,autoImportTranslationFunctions:!1,typedPages:!0,typedOptionsAndMessages:!1,generatedLocaleFilePathFormat:"absolute",alternateLinkCanonicalQueries:!1,hmr:!0},multiDomainLocales:!1}},apiSecret:"",content:{databaseVersion:"v3.5.0",version:"3.6.0",database:{type:"sqlite",filename:"./contents.sqlite"},localDatabase:{type:"sqlite",filename:"E:/WORK/test5/next-novel-website/.data/content/contents.sqlite"},integrityCheck:!0},icon:{serverKnownCssClasses:[]},ipx:{baseURL:"/_ipx",alias:{},fs:{dir:["E:/WORK/test5/next-novel-website/public"]},http:{domains:[]}}},mt={prefix:"NITRO_",altPrefix:ht.nitro.envPrefix??e.env.NITRO_ENV_PREFIX??"_",envExpansion:ht.nitro.envExpansion??e.env.NITRO_ENV_EXPANSION??!1},gt=_deepFreeze(applyEnv(F(ht),mt));function useRuntimeConfig(e){if(!e)return gt;if(e.context.nitro.runtimeConfig)return e.context.nitro.runtimeConfig;const t=F(ht);return applyEnv(t,mt),e.context.nitro.runtimeConfig=t,t}const yt=_deepFreeze(F(ft));function _deepFreeze(e){const t=Object.getOwnPropertyNames(e);for(const n of t){const t=e[n];t&&"object"==typeof t&&_deepFreeze(t)}return Object.freeze(e)}new Proxy(Object.create(null),{get:(e,t)=>{console.warn("Please use `useRuntimeConfig()` instead of accessing config directly.");const n=useRuntimeConfig();if(t in n)return n[t]}});const wt=ye(we({routes:useRuntimeConfig().nitro.routeRules}));function getRouteRules(e){return e.context._nitro=e.context._nitro||{},e.context._nitro.routeRules||(e.context._nitro.routeRules=getRouteRulesForPath(withoutBase(e.path.split("?")[0],useRuntimeConfig().app.baseURL))),e.context._nitro.routeRules}function getRouteRulesForPath(e){return q({},...wt.matchAll(e).reverse())}function _captureError(e,t){console.error(`[${t}]`,e),useNitroApp().captureError(e,{tags:[t]})}function joinHeaders(e){return Array.isArray(e)?e.join(", "):String(e)}function normalizeCookieHeader(e=""){return d(joinHeaders(e))}function normalizeCookieHeaders(e){const t=new Headers;for(const[n,o]of e)if("set-cookie"===n)for(const e of normalizeCookieHeader(o))t.append("set-cookie",e);else t.set(n,joinHeaders(o));return t}function hasReqHeader(e,t,n){const o=b(e,t);return o&&"string"==typeof o&&o.toLowerCase().includes(n)}async function defaultHandler(t,n,o){const r=t.unhandled||t.fatal,s=t.statusCode||500,i=t.statusMessage||"Server Error",a=k(n,{xForwardedHost:!0,xForwardedProto:!0});if(404===s){const e="/";if(/^\/[^/]/.test(e)&&!a.pathname.startsWith(e)){return{status:302,statusText:"Found",headers:{location:`${e}${a.pathname.slice(1)}${a.search}`},body:"Redirecting..."}}}await loadStackTrace(t).catch(xe.error);const c=new Re;if(r&&!o?.silent){const o=[t.unhandled&&"[unhandled]",t.fatal&&"[fatal]"].filter(Boolean).join(" "),r=await(await c.toANSI(t)).replaceAll(e.cwd(),".");xe.error(`[request error] ${o} [${n.method}] ${a}\n\n`,r)}const l=o?.json||!b(n,"accept")?.includes("text/html"),u={"content-type":l?"application/json":"text/html","x-content-type-options":"nosniff","x-frame-options":"DENY","referrer-policy":"no-referrer","content-security-policy":"script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"};404!==s&&S(n,"cache-control")||(u["cache-control"]="no-cache");return{status:s,statusText:i,headers:u,body:l?{error:!0,url:a,statusCode:s,statusMessage:i,message:t.message,data:t.data,stack:t.stack?.split("\n").map(e=>e.trim())}:await c.toHTML(t,{request:{url:a.href,method:n.method,headers:R(n)}})}}async function loadStackTrace(e){if(!(e instanceof Error))return;const t=await(new _e).defineSourceLoader(sourceLoader).parse(e),n=e.message+"\n"+t.frames.map(e=>function(e){if("native"===e.type)return e.raw;const t=`${e.fileName||""}:${e.lineNumber}:${e.columnNumber})`;return e.functionName?`at ${e.functionName} (${t}`:`at ${t}`}(e)).join("\n");Object.defineProperty(e,"stack",{value:n}),e.cause&&await loadStackTrace(e.cause).catch(xe.error)}async function sourceLoader(e){if(!e.fileName||"fs"!==e.fileType||"native"===e.type)return;if("app"===e.type){const t=await be(`${e.fileName}.map`,"utf8").catch(()=>{});if(t){const n=(await new Ee(t)).originalPositionFor({line:e.lineNumber,column:e.columnNumber});n.source&&n.line&&(e.fileName=o(r(e.fileName),n.source),e.lineNumber=n.line,e.columnNumber=n.column||0)}}const t=await be(e.fileName,"utf8").catch(()=>{});return t?{contents:t}:void 0}const bt=[async function(e,t,{defaultHandler:n}){if(t.handled||function(e){return!hasReqHeader(e,"accept","text/html")&&(hasReqHeader(e,"accept","application/json")||hasReqHeader(e,"user-agent","curl/")||hasReqHeader(e,"user-agent","httpie/")||hasReqHeader(e,"sec-fetch-mode","cors")||e.path.startsWith("/api/")||e.path.endsWith(".json"))}(t))return;const o=await n(e,t,{json:!0});if(404===(e.statusCode||500)&&302===o.status)return x(t,o.headers),v(t,o.status,o.statusText),_(t,JSON.stringify(o.body,null,2));"string"!=typeof o.body&&Array.isArray(o.body.stack)&&(o.body.stack=o.body.stack.join("\n"));const r=o.body,s=new URL(r.url);r.url=withoutBase(s.pathname,useRuntimeConfig(t).app.baseURL)+s.search+s.hash,r.message||="Server Error",r.data||=e.data,r.statusMessage||=e.statusMessage,delete o.headers["content-type"],delete o.headers["content-security-policy"],x(t,o.headers);const i=R(t),a=t.path.startsWith("/__nuxt_error")||!!i["x-nuxt-error"]?null:await useNitroApp().localFetch(withQuery(joinURL(useRuntimeConfig(t).app.baseURL,"/__nuxt_error"),r),{headers:{...i,"x-nuxt-error":"true"},redirect:"manual"}).catch(()=>null);if(t.handled)return;if(!a){const{template:e}=await Promise.resolve().then(function(){return fn});return r.description=r.message,E(t,"Content-Type","text/html;charset=UTF-8"),_(t,e(r))}const c=await a.text();for(const[e,n]of a.headers.entries())E(t,e,n);return v(t,a.status&&200!==a.status?a.status:o.status,a.statusText||o.statusText),_(t,c)},async function(e,t){const n=await defaultHandler(e,t);return t.node?.res.headersSent||x(t,n.headers),v(t,n.status,n.statusText),_(t,"string"==typeof n.body?n.body:JSON.stringify(n.body,null,2))}];const xt={meta:[{name:"viewport",content:"width=device-width, initial-scale=1"},{charset:"utf-8"}],link:[],style:[],script:[],noscript:[]},vt="div",_t={id:"teleports"},Rt="nuxt-app",Et={VNode:e=>te(e)?{type:e.type,props:e.props}:void 0,URL:e=>e instanceof URL?e.toString():void 0},kt=Se("nuxt-dev",{asyncContext:!0,AsyncLocalStorage:ke}),St=/\/node_modules\/(?:.*\/)?(?:nuxt|nuxt-nightly|nuxt-edge|nuxt3|consola|@vue)\/|core\/runtime\/nitro/;const jt=[function(e){e.hooks.hook("render:html",e=>{e.head.push("<script>\nif (!window.__NUXT_DEVTOOLS_TIME_METRIC__) {\n  Object.defineProperty(window, '__NUXT_DEVTOOLS_TIME_METRIC__', {\n    value: {},\n    enumerable: false,\n    configurable: true,\n  })\n}\nwindow.__NUXT_DEVTOOLS_TIME_METRIC__.appInit = Date.now()\n<\/script>")})},e=>{const t=e.h3App.handler;var n;e.h3App.handler=e=>kt.callAsync({logs:[],event:e},()=>t(e)),n=e=>{const t=kt.tryUse();if(!t)return;const n=je();if(!n||n.includes("runtime/vite-node.mjs"))return;const o=[];let r="";for(const e of Ae(n))e.source!==globalThis._importMeta_.url&&(St.test(e.source)||(r||=e.source.replace(withTrailingSlash("E:/WORK/test5/next-novel-website"),""),o.push({...e,source:e.source.startsWith("file://")?e.source.replace("file://",""):e.source})));const s={...e,filename:r,stack:o};t.logs.push(s)},ve.addReporter({log(e){n(e)}}),ve.wrapConsole(),e.hooks.hook("afterResponse",()=>{const t=kt.tryUse();if(t)return e.hooks.callHook("dev:ssr-logs",{logs:t.logs,path:t.event.path})}),e.hooks.hook("render:html",e=>{const t=kt.tryUse();if(t)try{const n=Object.assign(Object.create(null),Et,t.event.context._payloadReducers);e.bodyAppend.unshift(`<script type="application/json" data-nuxt-logs="${Rt}">${Z(t.logs,n)}<\/script>`)}catch(e){const t=e instanceof Error&&"toString"in e?` Received \`${e.toString()}\`.`:"";console.warn(`[nuxt] Failed to stringify dev server logs.${t} You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.`)}})},function(e){e.hooks.hook("render:html",e=>{e.head.push('<script>"use strict";(()=>{const t=window,e=document.documentElement,c=["dark","light"],n=getStorageValue("localStorage","nuxt-color-mode")||"dark";let i=n==="system"?u():n;const r=e.getAttribute("data-color-mode-forced");r&&(i=r),l(i),t["__NUXT_COLOR_MODE__"]={preference:n,value:i,getColorScheme:u,addColorScheme:l,removeColorScheme:d};function l(o){const s=""+o+"",a="";e.classList?e.classList.add(s):e.className+=" "+s,a&&e.setAttribute("data-"+a,o)}function d(o){const s=""+o+"",a="";e.classList?e.classList.remove(s):e.className=e.className.replace(new RegExp(s,"g"),""),a&&e.removeAttribute("data-"+a)}function f(o){return t.matchMedia("(prefers-color-scheme"+o+")")}function u(){if(t.matchMedia&&f("").media!=="not all"){for(const o of c)if(f(":"+o).matches)return o}return"dark"}})();function getStorageValue(t,e){switch(t){case"localStorage":return window.localStorage.getItem(e);case"sessionStorage":return window.sessionStorage.getItem(e);case"cookie":return getCookie(e);default:return null}}function getCookie(t){const c=("; "+window.document.cookie).split("; "+t+"=");if(c.length===2)return c.pop()?.split(";").shift()}<\/script>')})}];const At={},Ct={};function buildAssetsURL(...e){return joinRelativeURL(publicAssetsURL(),useRuntimeConfig().app.buildAssetsDir,...e)}function publicAssetsURL(...e){const t=useRuntimeConfig().app,n=t.cdnURL||t.baseURL;return e.length?joinRelativeURL(n,...e):n}const Ot={content:"v3.5.0--bgIYhpjRuV8zbHJE_CfelwKpJ_Td6YuGJwixiek8lmI"},Tt={content:"bgIYhpjRuV8zbHJE_CfelwKpJ_Td6YuGJwixiek8lmI"},Lt={content:"_content_content",info:"_content_info"},$t={content:{type:"page",fields:{id:"string",title:"string",body:"json",description:"string",extension:"string",meta:"json",navigation:"json",path:"string",seo:"json",stem:"string"}},info:{type:"data",fields:{}}};const Ut=new Set,Nt="https://api.iconify.design",It=defineCachedEventHandler(async e=>{const t=k(e);if(!t)return Ue({status:400,message:"Invalid icon request"});const n=yt.icon,o=e.context.params?.collection?.replace(/\.json$/,""),r=o?await(He[o]?.()):null,s=n.iconifyApiEndpoint||Nt,i=t.searchParams.get("icons")?.split(",");if(r){if(i?.length){const e=Te(r,i);return We.debug(`[Icon] serving ${(i||[]).map(e=>"`"+o+":"+e+"`").join(",")} from bundled collection`),e}}else o&&!Ut.has(o)&&s===Nt&&(We.warn([`[Icon] Collection \`${o}\` is not found locally`,`We suggest to install it via \`npm i -D @iconify-json/${o}\` to provide the best end-user experience.`].join("\n")),Ut.add(o));if(!0===n.fallbackToApi||"server-only"===n.fallbackToApi){const e=new URL("./"+Ce(t.pathname)+t.search,s);if(We.debug(`[Icon] fetching ${(i||[]).map(e=>"`"+o+":"+e+"`").join(",")} from iconify api`),e.host!==new URL(s).host)return Ue({status:400,message:"Invalid icon request"});try{return await $fetch(e.href)}catch(e){return We.error(e),404===e.status?Ue({status:404}):Ue({status:500,message:"Failed to fetch fallback icon"})}}return Ue({status:404})},{group:"nuxt",name:"icon",getKey(e){const t=e.context.params?.collection?.replace(/\.json$/,"")||"unknown",n=String($e(e).icons||"");return`${t}_${n.split(",")[0]}_${n.length}_${Le(n)}`},swr:!0,maxAge:604800}),VueResolver=(e,t)=>oe(t)?ne(t):t;function resolveUnrefHeadInput(e){return Pe(e,VueResolver)}function createHead(e={}){const t=X({...e,propResolvers:[VueResolver]});return t.install=function(e){return{install(t){t.config.globalProperties.$unhead=e,t.config.globalProperties.$head=e,t.provide("usehead",e)}}.install}(t),t}const Wt={disableDefaults:!0,disableCapoSorting:!1,plugins:[re,se,ie,ae]};function createSSRContext(e){return{url:e.path,event:e,runtimeConfig:useRuntimeConfig(e),noSSR:e.context.nuxt?.noSSR||!1,head:createHead(Wt),error:!1,nuxt:void 0,payload:{},_payloadReducers:Object.create(null),modules:new Set}}const Ht=`<${vt}${Y({id:"__nuxt",class:"isolate"})}>`,Pt=`</${vt}>`,getClientManifest=()=>import("file://E:/WORK/test5/next-novel-website/.nuxt/dist/server/client.manifest.mjs").then(e=>e.default||e).then(e=>"function"==typeof e?e():e),Kt=lazyCachedFunction(async()=>{const t=await getClientManifest();if(!t)throw new Error("client.manifest is not available");const n=await import("file://E:/WORK/test5/next-novel-website/.nuxt/dist/server/server.mjs").then(e=>e.default||e);if(!n)throw new Error("Server bundle is not available");const o=P(n,{manifest:t,renderToString:async function(t,n){const r=await D(t,n);e.env.NUXT_VITE_NODE_OPTIONS&&o.rendererContext.updateManifest(await getClientManifest());return Ht+r+Pt},buildAssetsURL:buildAssetsURL});return o}),zt=lazyCachedFunction(async()=>{const e=await getClientManifest(),t=await Promise.resolve().then(function(){return pn}).then(e=>e.template).catch(()=>"").then(e=>Ht+e+Pt),n=P(()=>()=>{},{manifest:e,renderToString:()=>t,buildAssetsURL:buildAssetsURL}),o=await n.renderToString({});return{rendererContext:n.rendererContext,renderToString:e=>{const t=useRuntimeConfig(e.event);return e.modules||=new Set,e.payload.serverRendered=!1,e.config={public:t.public,app:t.app},Promise.resolve(o)}}});function lazyCachedFunction(e){let t=null;return()=>(null===t&&(t=e().catch(e=>{throw t=null,e})),t)}const Mt=lazyCachedFunction(()=>Promise.resolve().then(function(){return hn}).then(e=>e.default||e));const Dt=new RegExp(`^<${vt}[^>]*>([\\s\\S]*)<\\/${vt}>$`);function getServerComponentHTML(e){const t=e.match(Dt);return t?.[1]||e}const Ft=/^uid=([^;]*);slot=(.*)$/,qt=/^uid=([^;]*);client=(.*)$/,Bt=/^island-slot=([^;]*);(.*)$/;function getSlotIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.slots).length)return;const t={};for(const[n,o]of Object.entries(e.islandContext.slots))t[n]={...o,fallback:e.teleports?.[`island-fallback=${n}`]};return t}function getClientIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.components).length)return;const t={};for(const[n,o]of Object.entries(e.islandContext.components)){const r=e.teleports?.[n]?.replaceAll("\x3c!--teleport start anchor--\x3e","")||"";t[n]={...o,html:r,slots:getComponentSlotTeleport(n,e.teleports??{})}}return t}function getComponentSlotTeleport(e,t){const n=Object.entries(t),o={};for(const[t,r]of n){const n=t.match(Bt);if(n){const[,t,s]=n;if(!s||e!==t)continue;o[s]=r}}return o}function replaceIslandTeleports(e,t){const{teleports:n,islandContext:o}=e;if(o||!n)return t;for(const e in n){const o=e.match(qt);if(o){const[,r,s]=o;if(!r||!s)continue;t=t.replace(new RegExp(` data-island-uid="${r}" data-island-component="${s}"[^>]*>`),t=>t+n[e]);continue}const r=e.match(Ft);if(r){const[,o,s]=r;if(!o||!s)continue;t=t.replace(new RegExp(` data-island-uid="${o}" data-island-slot="${s}"[^>]*>`),t=>t+n[e])}}return t}const Qt=/\.json(\?.*)?$/,Vt=l(async e=>{const t=useNitroApp();x(e,{"content-type":"application/json;charset=utf-8","x-powered-by":"Nuxt"});const n=await async function(e){let t=e.path||"";const n=t.substring(15).replace(Qt,"").split("_"),o=n.length>1?n.pop():void 0,r=n.join("_"),s="GET"===e.method?C(e):await O(e);return{url:"/",...s,id:o,name:r,props:V(s.props)||{},slots:{},components:{}}}(e),o={...createSSRContext(e),islandContext:n,noSSR:!1,url:n.url},r=await Kt(),s=await r.renderToString(o).catch(async e=>{throw await(o.nuxt?.hooks.callHook("app:error",e)),e}),i=await async function(e){const t=await Mt(),n=new Set;for(const o of e)if(o in t&&t[o])for(const e of await t[o]())n.add(e);return Array.from(n).map(e=>({innerHTML:e}))}(o.modules??[]);await(o.nuxt?.hooks.callHook("app:rendered",{ssrContext:o,renderResult:s})),i.length&&o.head.push({style:i});{const{styles:e}=K(o,r.rendererContext),t=[];for(const n of Object.values(e))"inline"in getQuery(n.file)||n.file.includes("scoped")&&!n.file.includes("pages/")&&t.push({rel:"stylesheet",href:r.rendererContext.buildAssetsURL(n.file),crossorigin:""});t.length&&o.head.push({link:t},{mode:"server"})}const a={};for(const e of o.head.entries.values())for(const[t,n]of Object.entries(resolveUnrefHeadInput(e.input))){const e=a[t];Array.isArray(e)&&e.push(...n),a[t]=n}a.link||=[],a.style||=[];const c={id:n.id,head:a,html:getServerComponentHTML(s.html),components:getClientIslandResponse(o),slots:getSlotIslandResponse(o)};return await t.hooks.callHook("render:island",c,{event:e,islandContext:n}),c});const Jt=m(async e=>{const t=T(e,"collection");L(e,"Content-Type","text/plain");const n=await useStorage().getItem("build:content:database.compressed.mjs")||"";if(n){const e=`export const ${t} = "`,o=String(n).split("\n").find(t=>t.startsWith(e));if(o)return o.substring(e.length,o.length-1)}return await import("file://E:/WORK/test5/next-novel-website/.nuxt/content/database.compressed.mjs").then(e=>e[t])});async function decompressSQLDump(e,t="gzip"){const n=Uint8Array.from(atob(e),e=>e.charCodeAt(0)),o=new Response(new Blob([n])),r=o.body?.pipeThrough(new DecompressionStream(t)),s=await new Response(r).text();return JSON.parse(s)}function refineContentFields(e,t){const n=function(e){const t=e.match(/FROM\s+(\w+)/);if(!t)return{};const n=$t[function(e){return e.replace(/^_content_/,"")}(t[1])];return n?.fields||{}}(e),o={...t};for(const e in o)"json"===n[e]&&o[e]&&"undefined"!==o[e]&&(o[e]=JSON.parse(o[e])),"boolean"===n[e]&&"undefined"!==o[e]&&(o[e]=Boolean(o[e]));for(const e in o)"NULL"===o[e]&&(o[e]=void 0);return o}let Xt;function loadDatabaseAdapter(t){const{database:n,localDatabase:o}=t;return Xt||(Xt=ze(function(t){if("d1"===t.type)return{...t,bindingName:t.bindingName||t.binding};if("sqlite"===t.type){const n={...t};if(":memory:"===t.filename)return{name:"memory"};if("filename"in t){const o=Ke(t?.filename||"")||":memory:"===t?.filename?t?.filename:new URL(t.filename,globalThis._importMeta_.url).pathname;n.path="win32"===e.platform&&o.startsWith("/")?o.slice(1):o}return n}return t}(o))),{all:async(e,t=[])=>Xt.prepare(e).all(...t).then(t=>(t||[]).map(t=>refineContentFields(e,t))),first:async(e,t=[])=>Xt.prepare(e).get(...t).then(t=>t?refineContentFields(e,t):t),exec:async(e,t=[])=>Xt.prepare(e).run(...t)}}const Yt={},Gt={};async function checkAndImportDatabaseIntegrity(e,t,n){!1!==Yt[String(t)]&&(Yt[String(t)]=!1,Gt[String(t)]=Gt[String(t)]||async function(e,t,n,o,r){const s=loadDatabaseAdapter(r),i=await s.first(`SELECT * FROM ${Lt.info} WHERE id = ?`,[`checksum_${t}`]).catch(()=>null);i?.version&&!String(i.version)?.startsWith(`${r.databaseVersion}--`)&&(await s.exec(`DROP TABLE IF EXISTS ${Lt.info}`),i.version="");const a=i?.structureVersion===o;if(i?.version){if(i.version===n)return i.ready||await async function(e,t){let n,o=0;await new Promise((r,s)=>{n=setInterval(async()=>{const i=await e.first(`SELECT ready FROM ${Lt.info} WHERE id = ?`,[`checksum_${t}`]).catch(()=>({ready:!0}));i?.ready&&(clearInterval(n),r(0)),o++>Zt&&(clearInterval(n),s(new Error("Waiting for another database initialization timed out")))},1e3)}).catch(e=>{throw e}).finally(()=>{n&&clearInterval(n)})}(s,t),!0;await s.exec(`DELETE FROM ${Lt.info} WHERE id = ?`,[`checksum_${t}`]),a||await s.exec(`DROP TABLE IF EXISTS ${Lt[t]}`)}const c=await async function(e,t){return await async function(e,t){return await $fetch(`/__nuxt_content/${t}/sql_dump.txt`,{context:e?{cloudflare:e.context.cloudflare}:{},responseType:"text",headers:{"content-type":"text/plain",...e?.node?.req?.headers?.cookie?{cookie:e.node.req.headers.cookie}:{}},query:{v:Ot[String(t)],t:Date.now()}})}(e,String(t)).catch(e=>(console.error("Failed to fetch compressed dump",e),""))}(e,t).then(decompressSQLDump),l=c.map(e=>e.split(" -- ").pop());let u=new Set;if(a){const e=new Set(l),n=await s.all(`SELECT __hash__ FROM ${Lt[t]}`).catch(()=>[]);u=new Set(n.map(e=>e.__hash__));const o=u.difference(e);o.size&&await s.exec(`DELETE FROM ${Lt[t]} WHERE __hash__ IN (${Array(o.size).fill("?").join(",")})`,Array.from(o))}await c.reduce(async(e,t,n)=>{await e;const o=l[n],r=t.substring(0,t.length-o.length-4);if(a){if("structure"===o)return Promise.resolve();if(u.has(o))return Promise.resolve()}await s.exec(r).catch(e=>{const n=e.message||"Unknown error";console.error(`Failed to execute SQL ${t}: ${n}`)})},Promise.resolve());const d=await s.first(`SELECT version FROM ${Lt.info} WHERE id = ?`,[`checksum_${t}`]).catch(()=>({version:""}));return d?.version===n}(e,t,Ot[String(t)],Tt[String(t)],n).then(e=>{Yt[String(t)]=!e}).catch(e=>{console.error("Database integrity check failed",e),Yt[String(t)]=!0,Gt[String(t)]=null})),Gt[String(t)]&&await Gt[String(t)]}const Zt=90;const en=/SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|\$/i,tn=/COUNT\((DISTINCT )?([a-z_]\w+|\*)\)/i,nn=/^SELECT (.*) FROM (\w+)( WHERE .*)? ORDER BY (["\w,\s]+) (ASC|DESC)( LIMIT \d+)?( OFFSET \d+)?$/;function cleanupQuery(e,t={removeString:!1}){let n=!1,o="",r="";for(let s=0;s<e.length;s++){const i=e[s],a=e[s-1],c=e[s+1];if("'"!==i&&'"'!==i){if(!n){if("-"===i&&"-"===c)return r;if("/"===i&&"*"===c){for(s+=2;s<e.length&&("*"!==e[s]||"/"!==e[s+1]);)s+=1;s+=2;continue}r+=i}}else{if(!t?.removeString){r+=i;continue}if(n){if(i!==o||c===o||a===o)continue;n=!1,o="";continue}n=!0,o=i}}return r}const _lazy_GztnGj=()=>Promise.resolve().then(function(){return vn}),on=[{route:"/__nuxt_error",handler:_lazy_GztnGj,lazy:!0,middleware:!1,method:void 0},{route:"/api/_nuxt_icon/:collection",handler:It,lazy:!1,middleware:!1,method:void 0},{route:"/__nuxt_island/**",handler:Vt,lazy:!1,middleware:!1,method:void 0},{route:"/__nuxt_content/:collection/sql_dump.txt",handler:Jt,lazy:!1,middleware:!1,method:void 0},{route:"/__nuxt_content/:collection/query",handler:m(async e=>{const{sql:t}=await O(e),n=T(e,"collection");!function(e,t){if(!e)throw new Error("Invalid query");if(cleanupQuery(e)!==e)throw new Error("Invalid query");const n=e.match(nn);if(!n)throw new Error("Invalid query");const[o,r,s,i,a,c,l,u]=n,d=r.trim().split(", ");if(1===d.length){if("*"!==d[0]&&!d[0].match(tn)&&!d[0].match(/^"[a-z_]\w+"$/i))throw new Error("Invalid query")}else if(!d.every(e=>e.match(/^"[a-z_]\w+"$/i)))throw new Error("Invalid query");if(s!==`_content_${t}`)throw new Error("Invalid query");if(i){if(!i.startsWith(" WHERE (")||!i.endsWith(")"))throw new Error("Invalid query");if(cleanupQuery(i,{removeString:!0}).match(en))throw new Error("Invalid query")}if(!(a+" "+c).split(", ").every(e=>e.match(/^("[a-zA-Z_]+"|[a-zA-Z_]+) (ASC|DESC)$/)))throw new Error("Invalid query");if(void 0!==l&&!l.match(/^ LIMIT \d+$/))throw new Error("Invalid query");if(void 0!==u&&!u.match(/^ OFFSET \d+$/))throw new Error("Invalid query")}(t,n);const o=useRuntimeConfig().content;return o.integrityCheck&&await checkAndImportDatabaseIntegrity(e,n,o),loadDatabaseAdapter(o).all(t)}),lazy:!1,middleware:!1,method:void 0},{route:"/_ipx/**",handler:Ne(()=>{const e=useRuntimeConfig().ipx||{},t=e?.fs?.dir?(Array.isArray(e.fs.dir)?e.fs.dir:[e.fs.dir]).map(e=>Oe(e)?e:Me(new URL(e,globalThis._importMeta_.url))):void 0,n=e.fs?.dir?De({...e.fs,dir:t}):void 0,o=e.http?.domains?Fe({...e.http}):void 0;if(!n&&!o)throw new Error("IPX storage is not configured!");const r={...e,storage:n||o,httpStorage:o},s=qe(r),i=Be(s);return Ie(e.baseURL,i)}),lazy:!1,middleware:!1,method:void 0},{route:"/_fonts/**",handler:_lazy_GztnGj,lazy:!0,middleware:!1,method:void 0},{route:"/**",handler:_lazy_GztnGj,lazy:!0,middleware:!1,method:void 0}];const rn=function(){const e=useRuntimeConfig(),t=ce(),captureError=(e,n={})=>{const o=t.callHookParallel("error",e,n).catch(e=>{console.error("Error while capturing another error",e)});if(n.event&&h(n.event)){const t=n.event.context.nitro?.errors;t&&t.push({error:e,context:n}),n.event.waitUntil&&n.event.waitUntil(o)}},n=$({debug:Q(!0),onError:(e,t)=>(captureError(e,{event:t,tags:["request"]}),async function(e,t){for(const n of bt)try{if(await n(e,t,{defaultHandler:defaultHandler}),t.handled)return}catch(e){console.error(e)}}(e,t)),onRequest:async e=>{e.context.nitro=e.context.nitro||{errors:[]};const t=e.node.req?.__unenv__;t?._platform&&(e.context={_platform:t?._platform,...t._platform,...e.context}),!e.context.waitUntil&&t?.waitUntil&&(e.context.waitUntil=t.waitUntil),e.fetch=(t,n)=>p(e,t,n,{fetch:localFetch}),e.$fetch=(t,n)=>p(e,t,n,{fetch:s}),e.waitUntil=t=>{e.context.nitro._waitUntilPromises||(e.context.nitro._waitUntilPromises=[]),e.context.nitro._waitUntilPromises.push(t),e.context.waitUntil&&e.context.waitUntil(t)},e.captureError=(t,n)=>{captureError(t,{event:e,...n})},await rn.hooks.callHook("request",e).catch(t=>{captureError(t,{event:e,tags:["request"]})})},onBeforeResponse:async(e,t)=>{await rn.hooks.callHook("beforeResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})},onAfterResponse:async(e,t)=>{await rn.hooks.callHook("afterResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})}}),o=U({preemptive:!0}),r=N(n),localFetch=(e,t)=>e.toString().startsWith("/")?de(r,e,t).then(e=>function(e){return e.headers.has("set-cookie")?new Response(e.body,{status:e.status,statusText:e.statusText,headers:normalizeCookieHeaders(e.headers)}):e}(e)):globalThis.fetch(e,t),s=le({fetch:localFetch,Headers:ue,defaults:{baseURL:e.app.baseURL}});var i;globalThis.$fetch=s,n.use((i={localFetch:localFetch},m(e=>{const t=getRouteRules(e);if(t.headers&&g(e,t.headers),t.redirect){let n=t.redirect.to;if(n.endsWith("/**")){let o=e.path;const r=t.redirect._redirectStripBase;r&&(o=withoutBase(o,r)),n=joinURL(n.slice(0,-3),o)}else e.path.includes("?")&&(n=withQuery(n,getQuery(e.path)));return y(e,n,t.redirect.statusCode)}if(t.proxy){let n=t.proxy.to;if(n.endsWith("/**")){let o=e.path;const r=t.proxy._proxyStripBase;r&&(o=withoutBase(o,r)),n=joinURL(n.slice(0,-3),o)}else e.path.includes("?")&&(n=withQuery(n,getQuery(e.path)));return w(e,n,{fetch:i.localFetch,...t.proxy})}})));for(const t of on){let r=t.lazy?I(t.handler):t.handler;if(t.middleware||!t.route){const o=(e.app.baseURL+(t.route||"/")).replace(/\/+/g,"/");n.use(o,r)}else{const e=getRouteRulesForPath(t.route.replace(/:\w+|\*\*/g,"_"));e.cache&&(r=dt(r,{group:"nitro/routes",...e.cache})),o.use(t.route,r,t.method)}}return n.use(e.app.baseURL,o.handler),{hooks:t,h3App:n,router:o,localCall:e=>fe(r,e),localFetch:localFetch,captureError:captureError}}();function useNitroApp(){return rn}!function(e){for(const t of jt)try{t(e)}catch(t){throw e.captureError(t,{tags:["plugin"]}),t}}(rn),globalThis.crypto||(globalThis.crypto=i);const{NITRO_NO_UNIX_SOCKET:sn,NITRO_DEV_WORKER_ID:an}=e.env;e.on("unhandledRejection",e=>_captureError(e,"unhandledRejection")),e.on("uncaughtException",e=>_captureError(e,"uncaughtException")),a?.on("message",e=>{e&&"shutdown"===e.event&&shutdown()});const cn=useNitroApp(),ln=new n(N(cn.h3App));let un;function listen(n=Boolean(sn||e.versions.webcontainer||"Bun"in globalThis&&"win32"===e.platform)){return new Promise((o,r)=>{try{un=ln.listen(n?0:function(){const n=`nitro-worker-${e.pid}-${c}-${an}-${Math.round(1e4*Math.random())}.sock`;if("win32"===e.platform)return s(String.raw`\\.\pipe`,n);if("linux"===e.platform){if(Number.parseInt(e.versions.node.split(".")[0],10)>=20)return`\0${n}`}return s(t(),n)}(),()=>{const e=ln.address();a?.postMessage({event:"listen",address:"string"==typeof e?{socketPath:e}:{host:"localhost",port:e?.port}}),o()})}catch(e){r(e)}})}async function shutdown(){ln.closeAllConnections?.(),await Promise.all([new Promise(e=>un?.close(e)),cn.hooks.callHook("close").catch(console.error)]),a?.postMessage({event:"exit"})}listen().catch(()=>listen(!0)).catch(e=>(console.error("Dev worker failed to listen:",e),shutdown())),cn.router.get("/_nitro/tasks",l(async e=>{const t=await Promise.all(Object.entries(At).map(async([e,t])=>{const n=await(t.resolve?.());return[e,{description:n?.meta?.description}]}));return{tasks:Object.fromEntries(t),scheduledTasks:false}})),cn.router.use("/_nitro/tasks/:name",l(async e=>{const t=T(e,"name"),n={...C(e),...await O(e).then(e=>e?.payload).catch(()=>({}))};return await async function(e,{payload:t={},context:n={}}={}){if(Ct[e])return Ct[e];if(!(e in At))throw A({message:`Task \`${e}\` is not available!`,statusCode:404});if(!At[e].resolve)throw A({message:`Task \`${e}\` is not implemented!`,statusCode:501});const o=await At[e].resolve(),r={name:e,payload:t,context:n};Ct[e]=o.run(r);try{return await Ct[e]}finally{delete Ct[e]}}(t,{payload:n})}));const dn={appName:"Nuxt",version:"",statusCode:500,statusMessage:"Server error",description:"An error occurred in the application and the page could not be served. If you are the application owner, check your server logs for details.",stack:""},fn=Object.freeze({__proto__:null,template:e=>(e={...dn,...e},'<!DOCTYPE html><html lang="en"><head><title>'+H(e.statusCode)+" - "+H(e.statusMessage||"Internal Server Error")+'</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0" name="viewport"><style>.spotlight{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);bottom:-40vh;filter:blur(30vh);height:60vh;opacity:.8}*,:after,:before{border-color:var(--un-default-border-color,#e5e7eb);border-style:solid;border-width:0;box-sizing:border-box}:after,:before{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}h1{font-size:inherit;font-weight:inherit}h1,p{margin:0}*,:after,:before{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 transparent;--un-ring-shadow:0 0 transparent;--un-shadow-inset: ;--un-shadow:0 0 transparent;--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.pointer-events-none{pointer-events:none}.fixed{position:fixed}.left-0{left:0}.right-0{right:0}.z-10{z-index:10}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-black\\/5{background-color:#0000000d}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-14{padding-top:3.5rem}.text-6xl{font-size:3.75rem;line-height:1}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:bg-white\\/10{background-color:#ffffff1a}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style><script>!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll(\'link[rel="modulepreload"]\'))r(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)})).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();<\/script></head><body class="antialiased bg-white dark:bg-black dark:text-white flex flex-col font-sans min-h-screen pt-14 px-10 text-black"><div class="fixed left-0 pointer-events-none right-0 spotlight"></div><h1 class="font-medium mb-6 sm:text-8xl text-6xl">'+H(e.statusCode)+'</h1><p class="font-light leading-tight mb-8 sm:text-2xl text-xl">'+H(e.description)+'</p><div class="bg-black/5 bg-white dark:bg-white/10 flex-1 h-auto overflow-y-auto rounded-t-md"><div class="font-light leading-tight p-8 text-xl z-10">'+H(e.stack)+"</div></div></body></html>")}),pn=Object.freeze({__proto__:null,template:""}),hn=Object.freeze({__proto__:null,default:{}});function renderPayloadJsonScript(e){const t={type:"application/json",innerHTML:e.data?Z(e.data,e.ssrContext._payloadReducers):"","data-nuxt-data":Rt,"data-ssr":!e.ssrContext.noSSR,id:"__NUXT_DATA__"};e.src&&(t["data-src"]=e.src);return[t,{innerHTML:`window.__NUXT__={};window.__NUXT__.config=${ee(e.ssrContext.config)}`}]}function splitPayload(e){const{data:t,prerenderedAt:n,...o}=e.payload;return{initial:{...o,prerenderedAt:n},payload:{data:t,prerenderedAt:n}}}const mn={omitLineBreaks:!1};globalThis.__buildAssetsURL=buildAssetsURL,globalThis.__publicAssetsURL=publicAssetsURL;const gn=!!_t.id,yn=gn?`<div${Y(_t)}>`:"",wn=gn?"</div>":"",bn=/^[^?]*\/_payload.json(?:\?.*)?$/,xn=function(e){const t=useRuntimeConfig();return m(async n=>{const o=useNitroApp(),r={event:n,render:e,response:void 0};if(await o.hooks.callHook("render:before",r),!r.response){if(n.path===`${t.app.baseURL}favicon.ico`)return E(n,"Content-Type","image/x-icon"),_(n,"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");if(r.response=await r.render(n),!r.response){const e=j(n);return v(n,200===e?500:e),_(n,"No response returned from render handler: "+n.path)}}return await o.hooks.callHook("render:response",r.response,r),r.response.headers&&x(n,r.response.headers),(r.response.statusCode||r.response.statusMessage)&&v(n,r.response.statusCode,r.response.statusMessage),r.response.body})}(async e=>{const t=useNitroApp(),n=e.path.startsWith("/__nuxt_error")?C(e):null;if(n&&!("__unenv__"in e.node.req))throw A({statusCode:404,statusMessage:"Page Not Found: /__nuxt_error"});const o=createSSRContext(e),r={mode:"server"};o.head.push(xt,r),n&&(n.statusCode&&=Number.parseInt(n.statusCode),function(e,t){e.error=!0,e.payload={error:t},e.url=t.url}(o,n));const s=bn.test(o.url);if(s){const t=o.url.substring(0,o.url.lastIndexOf("/"))||"/";o.url=t,e._path=e.node.req.url=t}const i=getRouteRules(e);!1===i.ssr&&(o.noSSR=!0);const a=await function(e){return e.noSSR?zt():Kt()}(o),c=await a.renderToString(o).catch(async e=>{if(o._renderResponse&&"skipping render"===e.message)return{};const t=!n&&o.payload?.error||e;throw await(o.nuxt?.hooks.callHook("app:error",t)),t}),l=[];if(await(o.nuxt?.hooks.callHook("app:rendered",{ssrContext:o,renderResult:c})),o._renderResponse)return o._renderResponse;if(o.payload?.error&&!n)throw o.payload.error;if(s){const e=function(e){return{body:Z(splitPayload(e).payload,e._payloadReducers),statusCode:j(e.event),statusMessage:W(e.event),headers:{"content-type":"application/json;charset=utf-8","x-powered-by":"Nuxt"}}}(o);return e}const u=i.noScripts,{styles:d,scripts:f}=K(o,a.rendererContext);o._preloadManifest&&!u&&o.head.push({link:[{rel:"preload",as:"fetch",fetchpriority:"low",crossorigin:"anonymous",href:buildAssetsURL(`builds/meta/${o.runtimeConfig.app.buildId}.json`)}]},{...r,tagPriority:"low"}),l.length&&o.head.push({style:l});const p=[];for(const e of Object.values(d))"inline"in getQuery(e.file)||p.push({rel:"stylesheet",href:a.rendererContext.buildAssetsURL(e.file),crossorigin:""});p.length&&o.head.push({link:p},r),u||(o.head.push({link:z(o,a.rendererContext)},r),o.head.push({link:M(o,a.rendererContext)},r),o.head.push({script:renderPayloadJsonScript({ssrContext:o,data:o.payload})},{...r,tagPosition:"bodyClose",tagPriority:"high"})),i.noScripts||o.head.push({script:Object.values(f).map(e=>({type:e.module?"module":null,src:a.rendererContext.buildAssetsURL(e.file),defer:!e.module||null,tagPosition:"head",crossorigin:""}))},r);const{headTags:h,bodyTags:m,bodyTagsOpen:g,htmlAttrs:y,bodyAttrs:w}=await G(o.head,mn),b={htmlAttrs:y?[y]:[],head:normalizeChunks([h]),bodyAttrs:w?[w]:[],bodyPrepend:normalizeChunks([g,o.teleports?.body]),body:[replaceIslandTeleports(o,c.html),yn+(gn?joinTags([o.teleports?.[`#${_t.id}`]]):"")+wn],bodyAppend:[m]};return await t.hooks.callHook("render:html",b,{event:e}),{body:(x=b,`<!DOCTYPE html><html${joinAttrs(x.htmlAttrs)}><head>${joinTags(x.head)}</head><body${joinAttrs(x.bodyAttrs)}>${joinTags(x.bodyPrepend)}${joinTags(x.body)}${joinTags(x.bodyAppend)}</body></html>`),statusCode:j(e),statusMessage:W(e),headers:{"content-type":"text/html;charset=utf-8","x-powered-by":"Nuxt"}};var x});function normalizeChunks(e){return e.filter(Boolean).map(e=>e.trim())}function joinTags(e){return e.join("")}function joinAttrs(e){return 0===e.length?"":" "+e.join(" ")}const vn=Object.freeze({__proto__:null,default:xn});
//# sourceMappingURL=index.mjs.map
