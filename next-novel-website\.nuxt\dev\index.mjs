import e from"node:process";globalThis._importMeta_={url:import.meta.url,env:e.env};import{tmpdir as t}from"node:os";import{Server as n}from"node:http";import{resolve as o,dirname as r,join as s}from"node:path";import i from"node:crypto";import{parentPort as a,threadId as l}from"node:worker_threads";import{defineEventHandler as c,handleCacheHeaders as u,splitCookiesString as d,createEvent as p,fetchWithEvent as f,isEvent as h,eventHandler as m,setHeaders as g,sendRedirect as y,proxyRequest as b,getRequestHeader as x,setResponseHeaders as w,setResponseStatus as v,send as _,getRequestHeaders as R,setResponseHeader as k,getRequestURL as A,getResponseHeader as C,getQuery as E,readBody as j,createApp as S,createRouter as O,toNodeListener as T,lazyEvent<PERSON>and<PERSON> as N,getResponseStatus as U,createError as $,getRouterParam as H,getResponseStatusText as L}from"file://E:/WORK/test5/next-novel-website/node_modules/h3/dist/index.mjs";import{escapeHtml as P}from"file://E:/WORK/test5/next-novel-website/node_modules/@vue/shared/dist/shared.cjs.js";import{createRenderer as W,getRequestDependencies as I,getPreloadLinks as K,getPrefetchLinks as z}from"file://E:/WORK/test5/next-novel-website/node_modules/vue-bundle-renderer/dist/runtime.mjs";import{parseURL as M,withoutBase as F,joinURL as q,getQuery as B,withQuery as D,withTrailingSlash as X,joinRelativeURL as V}from"file://E:/WORK/test5/next-novel-website/node_modules/ufo/dist/index.mjs";import{renderToString as J}from"file://E:/WORK/test5/next-novel-website/node_modules/vue/server-renderer/index.mjs";import{klona as Y}from"file://E:/WORK/test5/next-novel-website/node_modules/klona/dist/index.mjs";import G,{defuFn as Q}from"file://E:/WORK/test5/next-novel-website/node_modules/defu/dist/defu.mjs";import Z,{destr as ee}from"file://E:/WORK/test5/next-novel-website/node_modules/destr/dist/index.mjs";import{snakeCase as te}from"file://E:/WORK/test5/next-novel-website/node_modules/scule/dist/index.mjs";import{createHead as ne,propsToString as oe,renderSSRHead as re}from"file://E:/WORK/test5/next-novel-website/node_modules/unhead/dist/server.mjs";import{stringify as se,uneval as ie}from"file://E:/WORK/test5/next-novel-website/node_modules/devalue/index.js";import{isVNode as ae,toValue as le,isRef as ce}from"file://E:/WORK/test5/next-novel-website/node_modules/vue/index.mjs";import{DeprecationsPlugin as ue,PromisesPlugin as de,TemplateParamsPlugin as pe,AliasSortingPlugin as fe}from"file://E:/WORK/test5/next-novel-website/node_modules/unhead/dist/plugins.mjs";import{createHooks as he}from"file://E:/WORK/test5/next-novel-website/node_modules/hookable/dist/index.mjs";import{createFetch as me,Headers as ge}from"file://E:/WORK/test5/next-novel-website/node_modules/ofetch/dist/node.mjs";import{fetchNodeRequestHandler as ye,callNodeRequestHandler as be}from"file://E:/WORK/test5/next-novel-website/node_modules/node-mock-http/dist/index.mjs";import{createStorage as xe,prefixStorage as we}from"file://E:/WORK/test5/next-novel-website/node_modules/unstorage/dist/index.mjs";import ve from"file://E:/WORK/test5/next-novel-website/node_modules/unstorage/drivers/fs.mjs";import{digest as _e}from"file://E:/WORK/test5/next-novel-website/node_modules/ohash/dist/index.mjs";import{toRouteMatcher as Re,createRouter as ke}from"file://E:/WORK/test5/next-novel-website/node_modules/radix3/dist/index.mjs";import{readFile as Ae}from"node:fs/promises";import Ce,{consola as Ee}from"file://E:/WORK/test5/next-novel-website/node_modules/consola/dist/index.mjs";import{ErrorParser as je}from"file://E:/WORK/test5/next-novel-website/node_modules/youch-core/build/index.js";import{Youch as Se}from"file://E:/WORK/test5/next-novel-website/node_modules/youch/build/index.js";import{SourceMapConsumer as Oe}from"file://E:/WORK/test5/next-novel-website/node_modules/source-map/source-map.js";import{AsyncLocalStorage as Te}from"node:async_hooks";import{getContext as Ne}from"file://E:/WORK/test5/next-novel-website/node_modules/unctx/dist/index.mjs";import{captureRawStackTrace as Ue,parseRawStackTrace as $e}from"file://E:/WORK/test5/next-novel-website/node_modules/errx/dist/index.js";import{walkResolver as He}from"file://E:/WORK/test5/next-novel-website/node_modules/unhead/dist/utils.mjs";const Le=[{baseName:"server",dir:"E:/WORK/test5/next-novel-website/server/assets"}],Pe=xe();for(const e of Le)Pe.mount(e.baseName,ve({base:e.dir,ignore:e?.ignore||[]}));const We=xe({});function useStorage(e=""){return e?we(We,e):We}We.mount("/assets",Pe),We.mount("root",ve({driver:"fs",readOnly:!0,base:"E:/WORK/test5/next-novel-website",watchOptions:{ignored:[null]}})),We.mount("src",ve({driver:"fs",readOnly:!0,base:"E:/WORK/test5/next-novel-website/server",watchOptions:{ignored:[null]}})),We.mount("build",ve({driver:"fs",readOnly:!1,base:"E:/WORK/test5/next-novel-website/.nuxt"})),We.mount("cache",ve({driver:"fs",readOnly:!1,base:"E:/WORK/test5/next-novel-website/.nuxt/cache"})),We.mount("data",ve({driver:"fs",base:"E:/WORK/test5/next-novel-website/.data/kv"}));const Ie=(()=>{class Hasher2{buff="";#e=new Map;write(e){this.buff+=e}dispatch(e){return this[null===e?"null":typeof e](e)}object(e){if(e&&"function"==typeof e.toJSON)return this.object(e.toJSON());const t=Object.prototype.toString.call(e);let n="";const o=t.length;n=o<10?"unknown:["+t+"]":t.slice(8,o-1),n=n.toLowerCase();let r=null;if(void 0!==(r=this.#e.get(e)))return this.dispatch("[CIRCULAR:"+r+"]");if(this.#e.set(e,this.#e.size),"undefined"!=typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(e))return this.write("buffer:"),this.write(e.toString("utf8"));if("object"!==n&&"function"!==n&&"asyncfunction"!==n)this[n]?this[n](e):this.unknown(e,n);else{const t=Object.keys(e).sort(),n=[];this.write("object:"+(t.length+n.length)+":");const dispatchForKey=t=>{this.dispatch(t),this.write(":"),this.dispatch(e[t]),this.write(",")};for(const e of t)dispatchForKey(e);for(const e of n)dispatchForKey(e)}}array(e,t){if(t=void 0!==t&&t,this.write("array:"+e.length+":"),!t||e.length<=1){for(const t of e)this.dispatch(t);return}const n=new Map,o=e.map(e=>{const t=new Hasher2;t.dispatch(e);for(const[e,o]of t.#e)n.set(e,o);return t.toString()});return this.#e=n,o.sort(),this.array(o,!1)}date(e){return this.write("date:"+e.toJSON())}symbol(e){return this.write("symbol:"+e.toString())}unknown(e,t){if(this.write(t),e)return this.write(":"),e&&"function"==typeof e.entries?this.array([...e.entries()],!0):void 0}error(e){return this.write("error:"+e.toString())}boolean(e){return this.write("bool:"+e)}string(e){this.write("string:"+e.length+":"),this.write(e)}function(e){this.write("fn:"),!function(e){if("function"!=typeof e)return!1;return"[native code] }"===Function.prototype.toString.call(e).slice(-15)}(e)?this.dispatch(e.toString()):this.dispatch("[native]")}number(e){return this.write("number:"+e)}null(){return this.write("Null")}undefined(){return this.write("Undefined")}regexp(e){return this.write("regex:"+e.toString())}arraybuffer(e){return this.write("arraybuffer:"),this.dispatch(new Uint8Array(e))}url(e){return this.write("url:"+e.toString())}map(e){this.write("map:");const t=[...e];return this.array(t,!1)}set(e){this.write("set:");const t=[...e];return this.array(t,!1)}bigint(e){return this.write("bigint:"+e.toString())}}for(const e of["uint8array","uint8clampedarray","unt8array","uint16array","unt16array","uint32array","unt32array","float32array","float64array"])Hasher2.prototype[e]=function(t){return this.write(e+":"),this.array([...t],!1)};return Hasher2})();function hash(e){return _e("string"==typeof e?e:function(e){const t=new Ie;return t.dispatch(e),t.buff}(e)).replace(/[-_]/g,"").slice(0,10)}function defineCachedFunction(e,t={}){t={name:"_",base:"/cache",swr:!0,maxAge:1,...t};const n={},o=t.group||"nitro/functions",r=t.name||e.name||"_",s=t.integrity||hash([e,t]),i=t.validate||(e=>void 0!==e.value);return async(...a)=>{if(await(t.shouldBypassCache?.(...a)))return e(...a);const l=await(t.getKey||getKey)(...a),c=await(t.shouldInvalidateCache?.(...a)),u=await async function(e,a,l,c){const u=[t.base,o,r,e+".json"].filter(Boolean).join(":").replace(/:\/$/,":index");let d=await useStorage().getItem(u).catch(e=>{console.error("[cache] Cache read error.",e),useNitroApp().captureError(e,{event:c,tags:["cache"]})})||{};if("object"!=typeof d){d={};const e=new Error("Malformed data read from cache.");console.error("[cache]",e),useNitroApp().captureError(e,{event:c,tags:["cache"]})}const p=1e3*(t.maxAge??0);p&&(d.expires=Date.now()+p);const f=l||d.integrity!==s||p&&Date.now()-(d.mtime||0)>p||!1===i(d),h=f?(async()=>{const o=n[e];o||(void 0!==d.value&&(t.staleMaxAge||0)>=0&&!1===t.swr&&(d.value=void 0,d.integrity=void 0,d.mtime=void 0,d.expires=void 0),n[e]=Promise.resolve(a()));try{d.value=await n[e]}catch(t){throw o||delete n[e],t}if(!o&&(d.mtime=Date.now(),d.integrity=s,delete n[e],!1!==i(d))){let e;t.maxAge&&!t.swr&&(e={ttl:t.maxAge});const n=useStorage().setItem(u,d,e).catch(e=>{console.error("[cache] Cache write error.",e),useNitroApp().captureError(e,{event:c,tags:["cache"]})});c?.waitUntil&&c.waitUntil(n)}})():Promise.resolve();return void 0===d.value?await h:f&&c&&c.waitUntil&&c.waitUntil(h),t.swr&&!1!==i(d)?(h.catch(e=>{console.error("[cache] SWR handler error.",e),useNitroApp().captureError(e,{event:c,tags:["cache"]})}),d):h.then(()=>d)}(l,()=>e(...a),c,a[0]&&h(a[0])?a[0]:void 0);let d=u.value;return t.transform&&(d=await t.transform(u,...a)||d),d}}function getKey(...e){return e.length>0?hash(e):""}function escapeKey(e){return String(e).replace(/\W/g,"")}function cloneWithProxy(e,t){return new Proxy(e,{get:(e,n,o)=>n in t?t[n]:Reflect.get(e,n,o),set:(e,n,o,r)=>n in t?(t[n]=o,!0):Reflect.set(e,n,o,r)})}const cachedEventHandler=function(e,t={name:"_",base:"/cache",swr:!0,maxAge:1}){const n=(t.varies||[]).filter(Boolean).map(e=>e.toLowerCase()).sort(),o={...t,getKey:async e=>{const o=await(t.getKey?.(e));if(o)return escapeKey(o);const r=e.node.req.originalUrl||e.node.req.url||e.path;let s;try{s=escapeKey(decodeURI(M(r).pathname)).slice(0,16)||"index"}catch{s="-"}return[`${s}.${hash(r)}`,...n.map(t=>[t,e.node.req.headers[t]]).map(([e,t])=>`${escapeKey(e)}.${hash(t)}`)].join(":")},validate:e=>!!e.value&&(!(e.value.code>=400)&&(void 0!==e.value.body&&("undefined"!==e.value.headers.etag&&"undefined"!==e.value.headers["last-modified"]))),group:t.group||"nitro/handlers",integrity:t.integrity||hash([e,t])},r=function(e,t={}){return defineCachedFunction(e,t)}(async r=>{const s={};for(const e of n){const t=r.node.req.headers[e];void 0!==t&&(s[e]=t)}const i=cloneWithProxy(r.node.req,{headers:s}),a={};let l;const c=cloneWithProxy(r.node.res,{statusCode:200,writableEnded:!1,writableFinished:!1,headersSent:!1,closed:!1,getHeader:e=>a[e],setHeader(e,t){return a[e]=t,this},getHeaderNames:()=>Object.keys(a),hasHeader:e=>e in a,removeHeader(e){delete a[e]},getHeaders:()=>a,end(e,t,n){return"string"==typeof e&&(l=e),"function"==typeof t&&t(),"function"==typeof n&&n(),this},write:(e,t,n)=>("string"==typeof e&&(l=e),"function"==typeof t&&t(void 0),"function"==typeof n&&n(),!0),writeHead(e,t){if(this.statusCode=e,t){if(Array.isArray(t)||"string"==typeof t)throw new TypeError("Raw headers  is not supported.");for(const e in t){const n=t[e];void 0!==n&&this.setHeader(e,n)}}return this}}),u=p(i,c);u.fetch=(e,t)=>f(u,e,t,{fetch:useNitroApp().localFetch}),u.$fetch=(e,t)=>f(u,e,t,{fetch:globalThis.$fetch}),u.waitUntil=r.waitUntil,u.context=r.context,u.context.cache={options:o};const d=await e(u)||l,h=u.node.res.getHeaders();h.etag=String(h.Etag||h.etag||`W/"${hash(d)}"`),h["last-modified"]=String(h["Last-Modified"]||h["last-modified"]||(new Date).toUTCString());const m=[];t.swr?(t.maxAge&&m.push(`s-maxage=${t.maxAge}`),t.staleMaxAge?m.push(`stale-while-revalidate=${t.staleMaxAge}`):m.push("stale-while-revalidate")):t.maxAge&&m.push(`max-age=${t.maxAge}`),m.length>0&&(h["cache-control"]=m.join(", "));return{code:u.node.res.statusCode,headers:h,body:d}},o);return c(async n=>{if(t.headersOnly){if(u(n,{maxAge:t.maxAge}))return;return e(n)}const o=await r(n);if(n.node.res.headersSent||n.node.res.writableEnded)return o.body;if(!u(n,{modifiedTime:new Date(o.headers["last-modified"]),etag:o.headers.etag,maxAge:t.maxAge})){n.node.res.statusCode=o.code;for(const e in o.headers){const t=o.headers[e];"set-cookie"===e?n.node.res.appendHeader(e,d(t)):void 0!==t&&n.node.res.setHeader(e,t)}return o.body}})},Ke=Q({nuxt:{}});function getEnv(t,n){const o=te(t).toUpperCase();return Z(e.env[n.prefix+o]??e.env[n.altPrefix+o])}function _isObject(e){return"object"==typeof e&&!Array.isArray(e)}function applyEnv(e,t,n=""){for(const o in e){const r=n?`${n}_${o}`:o,s=getEnv(r,t);_isObject(e[o])?_isObject(s)?(e[o]={...e[o],...s},applyEnv(e[o],t,r)):void 0===s?applyEnv(e[o],t,r):e[o]=s??e[o]:e[o]=s??e[o],t.envExpansion&&"string"==typeof e[o]&&(e[o]=_expandFromEnv(e[o]))}return e}const ze=/\{\{([^{}]*)\}\}/g;function _expandFromEnv(t){return t.replace(ze,(t,n)=>e.env[n]||t)}const Me={app:{baseURL:"/",buildId:"dev",buildAssetsDir:"/_nuxt/",cdnURL:""},nitro:{envPrefix:"NUXT_",routeRules:{"/__nuxt_error":{cache:!1},"/_nuxt/builds/meta/**":{headers:{"cache-control":"public, max-age=31536000, immutable"}},"/_nuxt/builds/**":{headers:{"cache-control":"public, max-age=1, immutable"}}}},public:{apiBase:"/api",siteUrl:"http://localhost:3000"},apiSecret:""},Fe={prefix:"NITRO_",altPrefix:Me.nitro.envPrefix??e.env.NITRO_ENV_PREFIX??"_",envExpansion:Me.nitro.envExpansion??e.env.NITRO_ENV_EXPANSION??!1},qe=_deepFreeze(applyEnv(Y(Me),Fe));function useRuntimeConfig(e){if(!e)return qe;if(e.context.nitro.runtimeConfig)return e.context.nitro.runtimeConfig;const t=Y(Me);return applyEnv(t,Fe),e.context.nitro.runtimeConfig=t,t}function _deepFreeze(e){const t=Object.getOwnPropertyNames(e);for(const n of t){const t=e[n];t&&"object"==typeof t&&_deepFreeze(t)}return Object.freeze(e)}_deepFreeze(Y(Ke)),new Proxy(Object.create(null),{get:(e,t)=>{console.warn("Please use `useRuntimeConfig()` instead of accessing config directly.");const n=useRuntimeConfig();if(t in n)return n[t]}});const Be=Re(ke({routes:useRuntimeConfig().nitro.routeRules}));function getRouteRules(e){return e.context._nitro=e.context._nitro||{},e.context._nitro.routeRules||(e.context._nitro.routeRules=getRouteRulesForPath(F(e.path.split("?")[0],useRuntimeConfig().app.baseURL))),e.context._nitro.routeRules}function getRouteRulesForPath(e){return G({},...Be.matchAll(e).reverse())}function _captureError(e,t){console.error(`[${t}]`,e),useNitroApp().captureError(e,{tags:[t]})}function joinHeaders(e){return Array.isArray(e)?e.join(", "):String(e)}function normalizeCookieHeader(e=""){return d(joinHeaders(e))}function normalizeCookieHeaders(e){const t=new Headers;for(const[n,o]of e)if("set-cookie"===n)for(const e of normalizeCookieHeader(o))t.append("set-cookie",e);else t.set(n,joinHeaders(o));return t}function hasReqHeader(e,t,n){const o=x(e,t);return o&&"string"==typeof o&&o.toLowerCase().includes(n)}async function defaultHandler(t,n,o){const r=t.unhandled||t.fatal,s=t.statusCode||500,i=t.statusMessage||"Server Error",a=A(n,{xForwardedHost:!0,xForwardedProto:!0});if(404===s){const e="/";if(/^\/[^/]/.test(e)&&!a.pathname.startsWith(e)){return{status:302,statusText:"Found",headers:{location:`${e}${a.pathname.slice(1)}${a.search}`},body:"Redirecting..."}}}await loadStackTrace(t).catch(Ce.error);const l=new Se;if(r&&!o?.silent){const o=[t.unhandled&&"[unhandled]",t.fatal&&"[fatal]"].filter(Boolean).join(" "),r=await(await l.toANSI(t)).replaceAll(e.cwd(),".");Ce.error(`[request error] ${o} [${n.method}] ${a}\n\n`,r)}const c=o?.json||!x(n,"accept")?.includes("text/html"),u={"content-type":c?"application/json":"text/html","x-content-type-options":"nosniff","x-frame-options":"DENY","referrer-policy":"no-referrer","content-security-policy":"script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"};404!==s&&C(n,"cache-control")||(u["cache-control"]="no-cache");return{status:s,statusText:i,headers:u,body:c?{error:!0,url:a,statusCode:s,statusMessage:i,message:t.message,data:t.data,stack:t.stack?.split("\n").map(e=>e.trim())}:await l.toHTML(t,{request:{url:a.href,method:n.method,headers:R(n)}})}}async function loadStackTrace(e){if(!(e instanceof Error))return;const t=await(new je).defineSourceLoader(sourceLoader).parse(e),n=e.message+"\n"+t.frames.map(e=>function(e){if("native"===e.type)return e.raw;const t=`${e.fileName||""}:${e.lineNumber}:${e.columnNumber})`;return e.functionName?`at ${e.functionName} (${t}`:`at ${t}`}(e)).join("\n");Object.defineProperty(e,"stack",{value:n}),e.cause&&await loadStackTrace(e.cause).catch(Ce.error)}async function sourceLoader(e){if(!e.fileName||"fs"!==e.fileType||"native"===e.type)return;if("app"===e.type){const t=await Ae(`${e.fileName}.map`,"utf8").catch(()=>{});if(t){const n=(await new Oe(t)).originalPositionFor({line:e.lineNumber,column:e.columnNumber});n.source&&n.line&&(e.fileName=o(r(e.fileName),n.source),e.lineNumber=n.line,e.columnNumber=n.column||0)}}const t=await Ae(e.fileName,"utf8").catch(()=>{});return t?{contents:t}:void 0}const De=[async function(e,t,{defaultHandler:n}){if(t.handled||function(e){return!hasReqHeader(e,"accept","text/html")&&(hasReqHeader(e,"accept","application/json")||hasReqHeader(e,"user-agent","curl/")||hasReqHeader(e,"user-agent","httpie/")||hasReqHeader(e,"sec-fetch-mode","cors")||e.path.startsWith("/api/")||e.path.endsWith(".json"))}(t))return;const o=await n(e,t,{json:!0});if(404===(e.statusCode||500)&&302===o.status)return w(t,o.headers),v(t,o.status,o.statusText),_(t,JSON.stringify(o.body,null,2));"string"!=typeof o.body&&Array.isArray(o.body.stack)&&(o.body.stack=o.body.stack.join("\n"));const r=o.body,s=new URL(r.url);r.url=F(s.pathname,useRuntimeConfig(t).app.baseURL)+s.search+s.hash,r.message||="Server Error",r.data||=e.data,r.statusMessage||=e.statusMessage,delete o.headers["content-type"],delete o.headers["content-security-policy"],w(t,o.headers);const i=R(t),a=t.path.startsWith("/__nuxt_error")||!!i["x-nuxt-error"]?null:await useNitroApp().localFetch(D(q(useRuntimeConfig(t).app.baseURL,"/__nuxt_error"),r),{headers:{...i,"x-nuxt-error":"true"},redirect:"manual"}).catch(()=>null);if(t.handled)return;if(!a){const{template:e}=await Promise.resolve().then(function(){return Rt});return r.description=r.message,k(t,"Content-Type","text/html;charset=UTF-8"),_(t,e(r))}const l=await a.text();for(const[e,n]of a.headers.entries())k(t,e,n);return v(t,a.status&&200!==a.status?a.status:o.status,a.statusText||o.statusText),_(t,l)},async function(e,t){const n=await defaultHandler(e,t);return t.node?.res.headersSent||w(t,n.headers),v(t,n.status,n.statusText),_(t,"string"==typeof n.body?n.body:JSON.stringify(n.body,null,2))}];const Xe={meta:[{name:"viewport",content:"width=device-width, initial-scale=1"},{charset:"utf-8"}],link:[],style:[],script:[],noscript:[]},Ve="div",Je={id:"teleports"},Ye="nuxt-app",Ge={VNode:e=>ae(e)?{type:e.type,props:e.props}:void 0,URL:e=>e instanceof URL?e.toString():void 0},Qe=Ne("nuxt-dev",{asyncContext:!0,AsyncLocalStorage:Te}),Ze=/\/node_modules\/(?:.*\/)?(?:nuxt|nuxt-nightly|nuxt-edge|nuxt3|consola|@vue)\/|core\/runtime\/nitro/;const et=[function(e){e.hooks.hook("render:html",e=>{e.head.push("<script>\nif (!window.__NUXT_DEVTOOLS_TIME_METRIC__) {\n  Object.defineProperty(window, '__NUXT_DEVTOOLS_TIME_METRIC__', {\n    value: {},\n    enumerable: false,\n    configurable: true,\n  })\n}\nwindow.__NUXT_DEVTOOLS_TIME_METRIC__.appInit = Date.now()\n<\/script>")})},e=>{const t=e.h3App.handler;var n;e.h3App.handler=e=>Qe.callAsync({logs:[],event:e},()=>t(e)),n=e=>{const t=Qe.tryUse();if(!t)return;const n=Ue();if(!n||n.includes("runtime/vite-node.mjs"))return;const o=[];let r="";for(const e of $e(n))e.source!==globalThis._importMeta_.url&&(Ze.test(e.source)||(r||=e.source.replace(X("E:/WORK/test5/next-novel-website"),""),o.push({...e,source:e.source.startsWith("file://")?e.source.replace("file://",""):e.source})));const s={...e,filename:r,stack:o};t.logs.push(s)},Ee.addReporter({log(e){n(e)}}),Ee.wrapConsole(),e.hooks.hook("afterResponse",()=>{const t=Qe.tryUse();if(t)return e.hooks.callHook("dev:ssr-logs",{logs:t.logs,path:t.event.path})}),e.hooks.hook("render:html",e=>{const t=Qe.tryUse();if(t)try{const n=Object.assign(Object.create(null),Ge,t.event.context._payloadReducers);e.bodyAppend.unshift(`<script type="application/json" data-nuxt-logs="${Ye}">${se(t.logs,n)}<\/script>`)}catch(e){const t=e instanceof Error&&"toString"in e?` Received \`${e.toString()}\`.`:"";console.warn(`[nuxt] Failed to stringify dev server logs.${t} You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.`)}})},function(e){e.hooks.hook("render:html",e=>{e.head.push('<script>"use strict";(()=>{const t=window,e=document.documentElement,c=["dark","light"],n=getStorageValue("localStorage","nuxt-color-mode")||"dark";let i=n==="system"?u():n;const r=e.getAttribute("data-color-mode-forced");r&&(i=r),l(i),t["__NUXT_COLOR_MODE__"]={preference:n,value:i,getColorScheme:u,addColorScheme:l,removeColorScheme:d};function l(o){const s=""+o+"",a="";e.classList?e.classList.add(s):e.className+=" "+s,a&&e.setAttribute("data-"+a,o)}function d(o){const s=""+o+"",a="";e.classList?e.classList.remove(s):e.className=e.className.replace(new RegExp(s,"g"),""),a&&e.removeAttribute("data-"+a)}function f(o){return t.matchMedia("(prefers-color-scheme"+o+")")}function u(){if(t.matchMedia&&f("").media!=="not all"){for(const o of c)if(f(":"+o).matches)return o}return"dark"}})();function getStorageValue(t,e){switch(t){case"localStorage":return window.localStorage.getItem(e);case"sessionStorage":return window.sessionStorage.getItem(e);case"cookie":return getCookie(e);default:return null}}function getCookie(t){const c=("; "+window.document.cookie).split("; "+t+"=");if(c.length===2)return c.pop()?.split(";").shift()}<\/script>')})}],VueResolver=(e,t)=>ce(t)?le(t):t;function resolveUnrefHeadInput(e){return He(e,VueResolver)}function createHead(e={}){const t=ne({...e,propResolvers:[VueResolver]});return t.install=function(e){return{install(t){t.config.globalProperties.$unhead=e,t.config.globalProperties.$head=e,t.provide("usehead",e)}}.install}(t),t}const tt={disableDefaults:!0,disableCapoSorting:!1,plugins:[ue,de,pe,fe]};function createSSRContext(e){return{url:e.path,event:e,runtimeConfig:useRuntimeConfig(e),noSSR:e.context.nuxt?.noSSR||!1,head:createHead(tt),error:!1,nuxt:void 0,payload:{},_payloadReducers:Object.create(null),modules:new Set}}function buildAssetsURL(...e){return V(publicAssetsURL(),useRuntimeConfig().app.buildAssetsDir,...e)}function publicAssetsURL(...e){const t=useRuntimeConfig().app,n=t.cdnURL||t.baseURL;return e.length?V(n,...e):n}const nt=`<${Ve}${oe({id:"__nuxt"})}>`,ot=`</${Ve}>`,getClientManifest=()=>import("file://E:/WORK/test5/next-novel-website/.nuxt/dist/server/client.manifest.mjs").then(e=>e.default||e).then(e=>"function"==typeof e?e():e),rt=lazyCachedFunction(async()=>{const t=await getClientManifest();if(!t)throw new Error("client.manifest is not available");const n=await import("file://E:/WORK/test5/next-novel-website/.nuxt/dist/server/server.mjs").then(e=>e.default||e);if(!n)throw new Error("Server bundle is not available");const o=W(n,{manifest:t,renderToString:async function(t,n){const r=await J(t,n);e.env.NUXT_VITE_NODE_OPTIONS&&o.rendererContext.updateManifest(await getClientManifest());return nt+r+ot},buildAssetsURL:buildAssetsURL});return o}),st=lazyCachedFunction(async()=>{const e=await getClientManifest(),t=await Promise.resolve().then(function(){return kt}).then(e=>e.template).catch(()=>"").then(e=>nt+e+ot),n=W(()=>()=>{},{manifest:e,renderToString:()=>t,buildAssetsURL:buildAssetsURL}),o=await n.renderToString({});return{rendererContext:n.rendererContext,renderToString:e=>{const t=useRuntimeConfig(e.event);return e.modules||=new Set,e.payload.serverRendered=!1,e.config={public:t.public,app:t.app},Promise.resolve(o)}}});function lazyCachedFunction(e){let t=null;return()=>(null===t&&(t=e().catch(e=>{throw t=null,e})),t)}const it=lazyCachedFunction(()=>Promise.resolve().then(function(){return At}).then(e=>e.default||e));const at=new RegExp(`^<${Ve}[^>]*>([\\s\\S]*)<\\/${Ve}>$`);function getServerComponentHTML(e){const t=e.match(at);return t?.[1]||e}const lt=/^uid=([^;]*);slot=(.*)$/,ct=/^uid=([^;]*);client=(.*)$/,ut=/^island-slot=([^;]*);(.*)$/;function getSlotIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.slots).length)return;const t={};for(const[n,o]of Object.entries(e.islandContext.slots))t[n]={...o,fallback:e.teleports?.[`island-fallback=${n}`]};return t}function getClientIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.components).length)return;const t={};for(const[n,o]of Object.entries(e.islandContext.components)){const r=e.teleports?.[n]?.replaceAll("\x3c!--teleport start anchor--\x3e","")||"";t[n]={...o,html:r,slots:getComponentSlotTeleport(n,e.teleports??{})}}return t}function getComponentSlotTeleport(e,t){const n=Object.entries(t),o={};for(const[t,r]of n){const n=t.match(ut);if(n){const[,t,s]=n;if(!s||e!==t)continue;o[s]=r}}return o}function replaceIslandTeleports(e,t){const{teleports:n,islandContext:o}=e;if(o||!n)return t;for(const e in n){const o=e.match(ct);if(o){const[,r,s]=o;if(!r||!s)continue;t=t.replace(new RegExp(` data-island-uid="${r}" data-island-component="${s}"[^>]*>`),t=>t+n[e]);continue}const r=e.match(lt);if(r){const[,o,s]=r;if(!o||!s)continue;t=t.replace(new RegExp(` data-island-uid="${o}" data-island-slot="${s}"[^>]*>`),t=>t+n[e])}}return t}const dt=/\.json(\?.*)?$/,pt=c(async e=>{const t=useNitroApp();w(e,{"content-type":"application/json;charset=utf-8","x-powered-by":"Nuxt"});const n=await async function(e){let t=e.path||"";const n=t.substring(15).replace(dt,"").split("_"),o=n.length>1?n.pop():void 0,r=n.join("_"),s="GET"===e.method?E(e):await j(e);return{url:"/",...s,id:o,name:r,props:ee(s.props)||{},slots:{},components:{}}}(e),o={...createSSRContext(e),islandContext:n,noSSR:!1,url:n.url},r=await rt(),s=await r.renderToString(o).catch(async e=>{throw await(o.nuxt?.hooks.callHook("app:error",e)),e}),i=await async function(e){const t=await it(),n=new Set;for(const o of e)if(o in t&&t[o])for(const e of await t[o]())n.add(e);return Array.from(n).map(e=>({innerHTML:e}))}(o.modules??[]);await(o.nuxt?.hooks.callHook("app:rendered",{ssrContext:o,renderResult:s})),i.length&&o.head.push({style:i});{const{styles:e}=I(o,r.rendererContext),t=[];for(const n of Object.values(e))"inline"in B(n.file)||n.file.includes("scoped")&&!n.file.includes("pages/")&&t.push({rel:"stylesheet",href:r.rendererContext.buildAssetsURL(n.file),crossorigin:""});t.length&&o.head.push({link:t},{mode:"server"})}const a={};for(const e of o.head.entries.values())for(const[t,n]of Object.entries(resolveUnrefHeadInput(e.input))){const e=a[t];Array.isArray(e)&&e.push(...n),a[t]=n}a.link||=[],a.style||=[];const l={id:n.id,head:a,html:getServerComponentHTML(s.html),components:getClientIslandResponse(o),slots:getSlotIslandResponse(o)};return await t.hooks.callHook("render:island",l,{event:e,islandContext:n}),l});const _lazy_GztnGj=()=>Promise.resolve().then(function(){return Nt}),ft=[{route:"/__nuxt_error",handler:_lazy_GztnGj,lazy:!0,middleware:!1,method:void 0},{route:"/__nuxt_island/**",handler:pt,lazy:!1,middleware:!1,method:void 0},{route:"/**",handler:_lazy_GztnGj,lazy:!0,middleware:!1,method:void 0}];const ht=function(){const e=useRuntimeConfig(),t=he(),captureError=(e,n={})=>{const o=t.callHookParallel("error",e,n).catch(e=>{console.error("Error while capturing another error",e)});if(n.event&&h(n.event)){const t=n.event.context.nitro?.errors;t&&t.push({error:e,context:n}),n.event.waitUntil&&n.event.waitUntil(o)}},n=S({debug:Z(!0),onError:(e,t)=>(captureError(e,{event:t,tags:["request"]}),async function(e,t){for(const n of De)try{if(await n(e,t,{defaultHandler:defaultHandler}),t.handled)return}catch(e){console.error(e)}}(e,t)),onRequest:async e=>{e.context.nitro=e.context.nitro||{errors:[]};const t=e.node.req?.__unenv__;t?._platform&&(e.context={_platform:t?._platform,...t._platform,...e.context}),!e.context.waitUntil&&t?.waitUntil&&(e.context.waitUntil=t.waitUntil),e.fetch=(t,n)=>f(e,t,n,{fetch:localFetch}),e.$fetch=(t,n)=>f(e,t,n,{fetch:s}),e.waitUntil=t=>{e.context.nitro._waitUntilPromises||(e.context.nitro._waitUntilPromises=[]),e.context.nitro._waitUntilPromises.push(t),e.context.waitUntil&&e.context.waitUntil(t)},e.captureError=(t,n)=>{captureError(t,{event:e,...n})},await ht.hooks.callHook("request",e).catch(t=>{captureError(t,{event:e,tags:["request"]})})},onBeforeResponse:async(e,t)=>{await ht.hooks.callHook("beforeResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})},onAfterResponse:async(e,t)=>{await ht.hooks.callHook("afterResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})}}),o=O({preemptive:!0}),r=T(n),localFetch=(e,t)=>e.toString().startsWith("/")?ye(r,e,t).then(e=>function(e){return e.headers.has("set-cookie")?new Response(e.body,{status:e.status,statusText:e.statusText,headers:normalizeCookieHeaders(e.headers)}):e}(e)):globalThis.fetch(e,t),s=me({fetch:localFetch,Headers:ge,defaults:{baseURL:e.app.baseURL}});var i;globalThis.$fetch=s,n.use((i={localFetch:localFetch},m(e=>{const t=getRouteRules(e);if(t.headers&&g(e,t.headers),t.redirect){let n=t.redirect.to;if(n.endsWith("/**")){let o=e.path;const r=t.redirect._redirectStripBase;r&&(o=F(o,r)),n=q(n.slice(0,-3),o)}else if(e.path.includes("?")){const t=B(e.path);n=D(n,t)}return y(e,n,t.redirect.statusCode)}if(t.proxy){let n=t.proxy.to;if(n.endsWith("/**")){let o=e.path;const r=t.proxy._proxyStripBase;r&&(o=F(o,r)),n=q(n.slice(0,-3),o)}else if(e.path.includes("?")){const t=B(e.path);n=D(n,t)}return b(e,n,{fetch:i.localFetch,...t.proxy})}})));for(const t of ft){let r=t.lazy?N(t.handler):t.handler;if(t.middleware||!t.route){const o=(e.app.baseURL+(t.route||"/")).replace(/\/+/g,"/");n.use(o,r)}else{const e=getRouteRulesForPath(t.route.replace(/:\w+|\*\*/g,"_"));e.cache&&(r=cachedEventHandler(r,{group:"nitro/routes",...e.cache})),o.use(t.route,r,t.method)}}return n.use(e.app.baseURL,o.handler),{hooks:t,h3App:n,router:o,localCall:e=>be(r,e),localFetch:localFetch,captureError:captureError}}();function useNitroApp(){return ht}!function(e){for(const t of et)try{t(e)}catch(t){throw e.captureError(t,{tags:["plugin"]}),t}}(ht);const mt={},gt={};globalThis.crypto||(globalThis.crypto=i);const{NITRO_NO_UNIX_SOCKET:yt,NITRO_DEV_WORKER_ID:bt}=e.env;e.on("unhandledRejection",e=>_captureError(e,"unhandledRejection")),e.on("uncaughtException",e=>_captureError(e,"uncaughtException")),a?.on("message",e=>{e&&"shutdown"===e.event&&shutdown()});const xt=useNitroApp(),wt=new n(T(xt.h3App));let vt;function listen(n=Boolean(yt||e.versions.webcontainer||"Bun"in globalThis&&"win32"===e.platform)){return new Promise((o,r)=>{try{vt=wt.listen(n?0:function(){const n=`nitro-worker-${e.pid}-${l}-${bt}-${Math.round(1e4*Math.random())}.sock`;if("win32"===e.platform)return s(String.raw`\\.\pipe`,n);if("linux"===e.platform){if(Number.parseInt(e.versions.node.split(".")[0],10)>=20)return`\0${n}`}return s(t(),n)}(),()=>{const e=wt.address();a?.postMessage({event:"listen",address:"string"==typeof e?{socketPath:e}:{host:"localhost",port:e?.port}}),o()})}catch(e){r(e)}})}async function shutdown(){wt.closeAllConnections?.(),await Promise.all([new Promise(e=>vt?.close(e)),xt.hooks.callHook("close").catch(console.error)]),a?.postMessage({event:"exit"})}listen().catch(()=>listen(!0)).catch(e=>(console.error("Dev worker failed to listen:",e),shutdown())),xt.router.get("/_nitro/tasks",c(async e=>{const t=await Promise.all(Object.entries(mt).map(async([e,t])=>{const n=await(t.resolve?.());return[e,{description:n?.meta?.description}]}));return{tasks:Object.fromEntries(t),scheduledTasks:false}})),xt.router.use("/_nitro/tasks/:name",c(async e=>{const t=H(e,"name"),n={...E(e),...await j(e).then(e=>e?.payload).catch(()=>({}))};return await async function(e,{payload:t={},context:n={}}={}){if(gt[e])return gt[e];if(!(e in mt))throw $({message:`Task \`${e}\` is not available!`,statusCode:404});if(!mt[e].resolve)throw $({message:`Task \`${e}\` is not implemented!`,statusCode:501});const o=await mt[e].resolve(),r={name:e,payload:t,context:n};gt[e]=o.run(r);try{return await gt[e]}finally{delete gt[e]}}(t,{payload:n})}));const _t={appName:"Nuxt",version:"",statusCode:500,statusMessage:"Server error",description:"An error occurred in the application and the page could not be served. If you are the application owner, check your server logs for details.",stack:""},Rt=Object.freeze({__proto__:null,template:e=>(e={..._t,...e},'<!DOCTYPE html><html lang="en"><head><title>'+P(e.statusCode)+" - "+P(e.statusMessage||"Internal Server Error")+'</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0" name="viewport"><style>.spotlight{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);bottom:-40vh;filter:blur(30vh);height:60vh;opacity:.8}*,:after,:before{border-color:var(--un-default-border-color,#e5e7eb);border-style:solid;border-width:0;box-sizing:border-box}:after,:before{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}h1{font-size:inherit;font-weight:inherit}h1,p{margin:0}*,:after,:before{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 transparent;--un-ring-shadow:0 0 transparent;--un-shadow-inset: ;--un-shadow:0 0 transparent;--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.pointer-events-none{pointer-events:none}.fixed{position:fixed}.left-0{left:0}.right-0{right:0}.z-10{z-index:10}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-black\\/5{background-color:#0000000d}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-14{padding-top:3.5rem}.text-6xl{font-size:3.75rem;line-height:1}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:bg-white\\/10{background-color:#ffffff1a}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style><script>!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll(\'link[rel="modulepreload"]\'))r(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)})).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();<\/script></head><body class="antialiased bg-white dark:bg-black dark:text-white flex flex-col font-sans min-h-screen pt-14 px-10 text-black"><div class="fixed left-0 pointer-events-none right-0 spotlight"></div><h1 class="font-medium mb-6 sm:text-8xl text-6xl">'+P(e.statusCode)+'</h1><p class="font-light leading-tight mb-8 sm:text-2xl text-xl">'+P(e.description)+'</p><div class="bg-black/5 bg-white dark:bg-white/10 flex-1 h-auto overflow-y-auto rounded-t-md"><div class="font-light leading-tight p-8 text-xl z-10">'+P(e.stack)+"</div></div></body></html>")}),kt=Object.freeze({__proto__:null,template:""}),At=Object.freeze({__proto__:null,default:{}});function renderPayloadJsonScript(e){const t={type:"application/json",innerHTML:e.data?se(e.data,e.ssrContext._payloadReducers):"","data-nuxt-data":Ye,"data-ssr":!e.ssrContext.noSSR,id:"__NUXT_DATA__"};e.src&&(t["data-src"]=e.src);return[t,{innerHTML:`window.__NUXT__={};window.__NUXT__.config=${ie(e.ssrContext.config)}`}]}function splitPayload(e){const{data:t,prerenderedAt:n,...o}=e.payload;return{initial:{...o,prerenderedAt:n},payload:{data:t,prerenderedAt:n}}}const Ct={omitLineBreaks:!1};globalThis.__buildAssetsURL=buildAssetsURL,globalThis.__publicAssetsURL=publicAssetsURL;const Et=!!Je.id,jt=Et?`<div${oe(Je)}>`:"",St=Et?"</div>":"",Ot=/^[^?]*\/_payload.json(?:\?.*)?$/,Tt=function(e){const t=useRuntimeConfig();return m(async n=>{const o=useNitroApp(),r={event:n,render:e,response:void 0};if(await o.hooks.callHook("render:before",r),!r.response){if(n.path===`${t.app.baseURL}favicon.ico`)return k(n,"Content-Type","image/x-icon"),_(n,"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");if(r.response=await r.render(n),!r.response){const e=U(n);return v(n,200===e?500:e),_(n,"No response returned from render handler: "+n.path)}}return await o.hooks.callHook("render:response",r.response,r),r.response.headers&&w(n,r.response.headers),(r.response.statusCode||r.response.statusMessage)&&v(n,r.response.statusCode,r.response.statusMessage),r.response.body})}(async e=>{const t=useNitroApp(),n=e.path.startsWith("/__nuxt_error")?E(e):null;if(n&&!("__unenv__"in e.node.req))throw $({statusCode:404,statusMessage:"Page Not Found: /__nuxt_error"});const o=createSSRContext(e),r={mode:"server"};o.head.push(Xe,r),n&&(n.statusCode&&=Number.parseInt(n.statusCode),function(e,t){e.error=!0,e.payload={error:t},e.url=t.url}(o,n));const s=Ot.test(o.url);if(s){const t=o.url.substring(0,o.url.lastIndexOf("/"))||"/";o.url=t,e._path=e.node.req.url=t}const i=getRouteRules(e);!1===i.ssr&&(o.noSSR=!0);const a=await function(e){return e.noSSR?st():rt()}(o),l=await a.renderToString(o).catch(async e=>{if(o._renderResponse&&"skipping render"===e.message)return{};const t=!n&&o.payload?.error||e;throw await(o.nuxt?.hooks.callHook("app:error",t)),t}),c=[];if(await(o.nuxt?.hooks.callHook("app:rendered",{ssrContext:o,renderResult:l})),o._renderResponse)return o._renderResponse;if(o.payload?.error&&!n)throw o.payload.error;if(s){const e=function(e){return{body:se(splitPayload(e).payload,e._payloadReducers),statusCode:U(e.event),statusMessage:L(e.event),headers:{"content-type":"application/json;charset=utf-8","x-powered-by":"Nuxt"}}}(o);return e}const u=i.noScripts,{styles:d,scripts:p}=I(o,a.rendererContext);o._preloadManifest&&!u&&o.head.push({link:[{rel:"preload",as:"fetch",fetchpriority:"low",crossorigin:"anonymous",href:buildAssetsURL(`builds/meta/${o.runtimeConfig.app.buildId}.json`)}]},{...r,tagPriority:"low"}),c.length&&o.head.push({style:c});const f=[];for(const e of Object.values(d))"inline"in B(e.file)||f.push({rel:"stylesheet",href:a.rendererContext.buildAssetsURL(e.file),crossorigin:""});f.length&&o.head.push({link:f},r),u||(o.head.push({link:K(o,a.rendererContext)},r),o.head.push({link:z(o,a.rendererContext)},r),o.head.push({script:renderPayloadJsonScript({ssrContext:o,data:o.payload})},{...r,tagPosition:"bodyClose",tagPriority:"high"})),i.noScripts||o.head.push({script:Object.values(p).map(e=>({type:e.module?"module":null,src:a.rendererContext.buildAssetsURL(e.file),defer:!e.module||null,tagPosition:"head",crossorigin:""}))},r);const{headTags:h,bodyTags:m,bodyTagsOpen:g,htmlAttrs:y,bodyAttrs:b}=await re(o.head,Ct),x={htmlAttrs:y?[y]:[],head:normalizeChunks([h]),bodyAttrs:b?[b]:[],bodyPrepend:normalizeChunks([g,o.teleports?.body]),body:[replaceIslandTeleports(o,l.html),jt+(Et?joinTags([o.teleports?.[`#${Je.id}`]]):"")+St],bodyAppend:[m]};return await t.hooks.callHook("render:html",x,{event:e}),{body:(w=x,`<!DOCTYPE html><html${joinAttrs(w.htmlAttrs)}><head>${joinTags(w.head)}</head><body${joinAttrs(w.bodyAttrs)}>${joinTags(w.bodyPrepend)}${joinTags(w.body)}${joinTags(w.bodyAppend)}</body></html>`),statusCode:U(e),statusMessage:L(e),headers:{"content-type":"text/html;charset=utf-8","x-powered-by":"Nuxt"}};var w});function normalizeChunks(e){return e.filter(Boolean).map(e=>e.trim())}function joinTags(e){return e.join("")}function joinAttrs(e){return 0===e.length?"":" "+e.join(" ")}const Nt=Object.freeze({__proto__:null,default:Tt});
//# sourceMappingURL=index.mjs.map
