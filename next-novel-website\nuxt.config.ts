// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // 模块配置
  modules: [
    '@nuxt/content',
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@nuxtjs/i18n',
    '@nuxt/eslint',
    '@nuxt/image',
    '@nuxt/ui',
    '@vueuse/nuxt',
    '@nuxtjs/color-mode'
  ],

  // TypeScript 配置
  typescript: {
    strict: true,
    typeCheck: true
  },

  // CSS 配置
  css: [
    '~/assets/css/main.css'
  ],

  // 国际化配置
  i18n: {
    locales: [
      { code: 'zh-CN', name: '中文', file: 'zh-CN.json' },
      { code: 'en', name: 'English', file: 'en.json' }
    ],
    defaultLocale: 'zh-CN',
    langDir: 'locales/',
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    },
    bundle: {
      optimizeTranslationDirective: false
    }
  },

  // 颜色模式配置
  colorMode: {
    preference: 'dark',
    fallback: 'dark',
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },

  // 响应式断点配置
  tailwindcss: {
    config: {
      theme: {
        screens: {
          'xs': '420px',
          'sm': '640px',
          'md': '768px',
          'lg': '1024px',
          'xl': '1280px'
        }
      }
    }
  },

  // 图片优化配置
  image: {
    quality: 80,
    format: ['webp', 'avif'],
    screens: {
      xs: 420,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280
    }
  },

  // 性能优化配置
  nitro: {
    compressPublicAssets: true,
    minify: true
  },

  // 构建配置
  build: {
    transpile: ['gsap']
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅服务端可用）
    apiSecret: '',

    // 公共配置（客户端和服务端都可用）
    public: {
      apiBase: '/api',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    }
  }
})