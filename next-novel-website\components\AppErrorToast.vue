<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="opacity-0 translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-2"
    >
      <div
        v-if="appStore.error"
        class="fixed top-4 right-4 z-50 max-w-sm w-full"
      >
        <div class="glass p-4 rounded-lg border border-red-500/20 bg-red-500/10">
          <div class="flex items-start space-x-3">
            <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-red-300">
                错误
              </p>
              <p class="text-sm text-red-200 mt-1">
                {{ appStore.error.message }}
              </p>
              <div v-if="appStore.error.code" class="text-xs text-red-400 mt-1">
                错误代码: {{ appStore.error.code }}
              </div>
            </div>
            <button
              class="flex-shrink-0 p-1 text-red-400 hover:text-red-300 transition-colors duration-200"
              @click="appStore.clearError()"
            >
              <Icon name="heroicons:x-mark" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()

// 自动清除错误（5秒后）
watch(() => appStore.error, (error) => {
  if (error) {
    setTimeout(() => {
      appStore.clearError()
    }, 5000)
  }
})
</script>
