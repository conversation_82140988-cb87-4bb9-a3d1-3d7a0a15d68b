export const AppErrorToast = () => import('./../../components/AppErrorToast.vue')
export const AppFooter = () => import('./../../components/AppFooter.vue')
export const AppNavigation = () => import('./../../components/AppNavigation.vue')
export const DemoSection = () => import('./../../components/DemoSection.vue')
export const LanguageSwitcher = () => import('./../../components/LanguageSwitcher.vue')
export const PricingCards = () => import('./../../components/PricingCards.vue')
export const TestimonialCarousel = () => import('./../../components/TestimonialCarousel.vue')
export const UAccordion = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Accordion.vue')
export const UAlert = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Alert.vue')
export const UApp = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/App.vue')
export const UAvatar = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Avatar.vue')
export const UAvatarGroup = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/AvatarGroup.vue')
export const UBadge = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Badge.vue')
export const UBreadcrumb = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Breadcrumb.vue')
export const UButton = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Button.vue')
export const UButtonGroup = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/ButtonGroup.vue')
export const UCalendar = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Calendar.vue')
export const UCard = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Card.vue')
export const UCarousel = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Carousel.vue')
export const UCheckbox = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Checkbox.vue')
export const UCheckboxGroup = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/CheckboxGroup.vue')
export const UChip = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Chip.vue')
export const UCollapsible = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Collapsible.vue')
export const UColorPicker = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/ColorPicker.vue')
export const UCommandPalette = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/CommandPalette.vue')
export const UContainer = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Container.vue')
export const UContextMenu = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/ContextMenu.vue')
export const UContextMenuContent = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/ContextMenuContent.vue')
export const UDrawer = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Drawer.vue')
export const UDropdownMenu = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/DropdownMenu.vue')
export const UDropdownMenuContent = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/DropdownMenuContent.vue')
export const UForm = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Form.vue')
export const UFormField = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/FormField.vue')
export const UIcon = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Icon.vue')
export const UInput = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Input.vue')
export const UInputMenu = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/InputMenu.vue')
export const UInputNumber = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/InputNumber.vue')
export const UKbd = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Kbd.vue')
export const ULink = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Link.vue')
export const ULinkBase = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/LinkBase.vue')
export const UModal = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Modal.vue')
export const UNavigationMenu = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/NavigationMenu.vue')
export const UOverlayProvider = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/OverlayProvider.vue')
export const UPagination = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Pagination.vue')
export const UPinInput = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/PinInput.vue')
export const UPopover = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Popover.vue')
export const UProgress = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Progress.vue')
export const URadioGroup = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/RadioGroup.vue')
export const USelect = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Select.vue')
export const USelectMenu = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/SelectMenu.vue')
export const USeparator = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Separator.vue')
export const USkeleton = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Skeleton.vue')
export const USlideover = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Slideover.vue')
export const USlider = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Slider.vue')
export const UStepper = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Stepper.vue')
export const USwitch = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Switch.vue')
export const UTable = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Table.vue')
export const UTabs = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Tabs.vue')
export const UTextarea = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Textarea.vue')
export const UToast = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Toast.vue')
export const UToaster = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Toaster.vue')
export const UTooltip = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Tooltip.vue')
export const UTree = () => import('./../../../node_modules/@nuxt/ui/dist/runtime/components/Tree.vue')
export const globalComponents: string[] = ["ProseA","ProseBlockquote","ProseCode","ProseEm","ProseH1","ProseH2","ProseH3","ProseH4","ProseH5","ProseH6","ProseHr","ProseImg","ProseLi","ProseOl","ProseP","ProsePre","ProseScript","ProseStrong","ProseTable","ProseTbody","ProseTd","ProseTh","ProseThead","ProseTr","ProseUl"]
export const localComponents: string[] = ["AppErrorToast","AppFooter","AppNavigation","DemoSection","LanguageSwitcher","PricingCards","TestimonialCarousel","UAccordion","UAlert","UApp","UAvatar","UAvatarGroup","UBadge","UBreadcrumb","UButton","UButtonGroup","UCalendar","UCard","UCarousel","UCheckbox","UCheckboxGroup","UChip","UCollapsible","UColorPicker","UCommandPalette","UContainer","UContextMenu","UContextMenuContent","UDrawer","UDropdownMenu","UDropdownMenuContent","UForm","UFormField","UIcon","UInput","UInputMenu","UInputNumber","UKbd","ULink","ULinkBase","UModal","UNavigationMenu","UOverlayProvider","UPagination","UPinInput","UPopover","UProgress","URadioGroup","USelect","USelectMenu","USeparator","USkeleton","USlideover","USlider","UStepper","USwitch","UTable","UTabs","UTextarea","UToast","UToaster","UTooltip","UTree"]