@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义 CSS 变量 */
:root {
  --color-primary: #6366f1;
  --color-secondary: #8b5cf6;
  --color-accent: #06b6d4;
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-text: #f8fafc;
  --color-text-muted: #94a3b8;

  /* 动效相关 */
  --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
  --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
}

/* 基础样式重置 */
html {
  scroll-behavior: smooth;
}

body {
  background-color: #0f172a;
  color: #f8fafc;
  font-family: ui-sans-serif, system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* 响应 prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 组件样式 */
.btn-magnetic {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.btn-magnetic::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, #6366f1, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: inherit;
}

.btn-magnetic:hover::before {
  opacity: 0.2;
}

/* 渐变文字 */
.text-gradient {
  background: linear-gradient(to right, #818cf8, #a78bfa, #67e8f9);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* 玻璃态效果 */
.glass {
  backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: scale(1.05);
  box-shadow: 0 25px 50px -12px rgba(99, 102, 241, 0.25);
}

/* 滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(71 85 105) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(71 85 105);
  border-radius: 9999px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(100 116 139);
}

/* 工具类 */
.selection-primary ::selection {
  background-color: rgba(99, 102, 241, 0.3);
  color: white;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1, 0 0 0 4px rgba(99, 102, 241, 0.2);
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 动效相关工具类 */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out-cubic) forwards;
}

.animate-slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out-cubic) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-out-cubic) forwards;
}

/* 关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式字体大小 */
.text-hero {
  font-size: 2.25rem; /* 36px */
  line-height: 2.5rem; /* 40px */
}

@media (min-width: 420px) {
  .text-hero {
    font-size: 2.25rem; /* 36px */
    line-height: 2.5rem; /* 40px */
  }
}

@media (min-width: 640px) {
  .text-hero {
    font-size: 3rem; /* 48px */
    line-height: 1;
  }
}

@media (min-width: 768px) {
  .text-hero {
    font-size: 3.75rem; /* 60px */
    line-height: 1;
  }
}

@media (min-width: 1024px) {
  .text-hero {
    font-size: 4.5rem; /* 72px */
    line-height: 1;
  }
}

@media (min-width: 1280px) {
  .text-hero {
    font-size: 6rem; /* 96px */
    line-height: 1;
  }
}
