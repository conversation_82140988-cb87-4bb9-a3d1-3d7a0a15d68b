@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义 CSS 变量 */
:root {
  --color-primary: #6366f1;
  --color-secondary: #8b5cf6;
  --color-accent: #06b6d4;
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-text: #f8fafc;
  --color-text-muted: #94a3b8;
  
  /* 动效相关 */
  --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
  --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
}

/* 基础样式重置 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-slate-900 text-slate-100 font-sans antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  /* 响应 prefers-reduced-motion */
  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }
    
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* 组件样式 */
@layer components {
  /* 磁性按钮基础样式 */
  .btn-magnetic {
    @apply relative overflow-hidden transition-all duration-300 ease-out;
    transform-style: preserve-3d;
  }
  
  .btn-magnetic::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 opacity-0 transition-opacity duration-300;
    border-radius: inherit;
  }
  
  .btn-magnetic:hover::before {
    @apply opacity-20;
  }
  
  /* 渐变文字 */
  .text-gradient {
    @apply bg-gradient-to-r from-indigo-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent;
  }
  
  /* 玻璃态效果 */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }
  
  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-300 ease-out hover:scale-105 hover:shadow-2xl hover:shadow-indigo-500/25;
  }
  
  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(71 85 105) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-slate-600 rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-500;
  }
}

/* 工具类 */
@layer utilities {
  /* 文字选择样式 */
  .selection-primary {
    @apply selection:bg-indigo-500/30 selection:text-white;
  }
  
  /* 焦点样式 */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-slate-900;
  }
  
  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* 动效相关工具类 */
  .animate-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-out-cubic) forwards;
  }
  
  .animate-slide-up {
    animation: slideUp var(--duration-normal) var(--ease-out-cubic) forwards;
  }
  
  .animate-scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-out-cubic) forwards;
  }
}

/* 关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式字体大小 */
@screen xs {
  .text-hero {
    @apply text-4xl;
  }
}

@screen sm {
  .text-hero {
    @apply text-5xl;
  }
}

@screen md {
  .text-hero {
    @apply text-6xl;
  }
}

@screen lg {
  .text-hero {
    @apply text-7xl;
  }
}

@screen xl {
  .text-hero {
    @apply text-8xl;
  }
}
