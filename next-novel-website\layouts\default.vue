<template>
  <div class="min-h-screen bg-slate-900 text-slate-100 selection-primary">
    <!-- 导航栏 -->
    <AppNavigation />
    
    <!-- 主要内容 -->
    <main class="relative">
      <slot />
    </main>
    
    <!-- 页脚 -->
    <AppFooter />
    
    <!-- 全局错误提示 -->
    <AppErrorToast />
    
    <!-- 移动端菜单遮罩 -->
    <div
      v-if="appStore.isMobileMenuOpen"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
      @click="appStore.closeMobileMenu()"
    />
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()

// 初始化应用
onMounted(() => {
  appStore.initialize()
})

// 监听路由变化，关闭移动端菜单
const route = useRoute()
watch(() => route.path, () => {
  appStore.closeMobileMenu()
})

// 监听滚动，控制导航栏显示/隐藏
let lastScrollY = 0
let ticking = false

const handleScroll = () => {
  if (!ticking) {
    requestAnimationFrame(() => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > 100) {
        if (currentScrollY > lastScrollY) {
          // 向下滚动，隐藏导航栏
          appStore.setNavVisibility(false)
        } else {
          // 向上滚动，显示导航栏
          appStore.setNavVisibility(true)
        }
      } else {
        // 在顶部，始终显示导航栏
        appStore.setNavVisibility(true)
      }
      
      lastScrollY = currentScrollY
      ticking = false
    })
    ticking = true
  }
}

onMounted(() => {
  if (process.client) {
    window.addEventListener('scroll', handleScroll, { passive: true })
  }
})

onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('scroll', handleScroll)
  }
})

// SEO 和 Meta 标签
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'format-detection', content: 'telephone=no' }
  ]
})
</script>

<style scoped>
/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

.page-enter-from {
  opacity: 0;
  transform: translateY(1rem);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-1rem);
}
</style>
