declare module '@capsizecss/metrics/zenKakuGothicAntique/900' {
  interface ZenKakuGothicAntiqueMetrics {
    familyName: string;
    fullName: string;
    postscriptName: string;
    category: string;
    capHeight: number;
    ascent: number;
    descent: number;
    lineGap: number;
    unitsPerEm: number;
    xHeight: number;
    xWidthAvg: number;
    subsets: Record<
      'latin' | 'thai',
      {
        xWidthAvg: number;
      }
    >;
  }
  export const fontMetrics: ZenKakuGothicAntiqueMetrics;
  export default fontMetrics;
}
