<template>
  <div class="overflow-hidden">
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-500/20 rounded-full blur-3xl"></div>
      </div>
      
      <div class="relative max-w-7xl mx-auto text-center">
        <div class="space-y-8">
          <!-- 主标题 -->
          <h1 class="text-hero font-bold leading-tight">
            <span class="block text-gradient">
              {{ $t('hero.title') }}
            </span>
          </h1>
          
          <!-- 副标题 -->
          <div class="text-xl sm:text-2xl text-slate-300 max-w-3xl mx-auto">
            <span>{{ $t('hero.subtitle', { role: currentRole }) }}</span>
          </div>
          
          <!-- 描述 -->
          <p class="text-lg text-slate-400 max-w-4xl mx-auto leading-relaxed">
            {{ $t('hero.description') }}
          </p>
          
          <!-- CTA 按钮 -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <NuxtLink
              to="/signup"
              class="btn-magnetic px-8 py-4 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl hover:shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 transform hover:scale-105"
            >
              {{ $t('hero.cta.primary') }}
            </NuxtLink>
            <button
              class="px-8 py-4 border border-slate-600 text-slate-300 font-semibold rounded-xl hover:border-slate-500 hover:text-white transition-all duration-300"
              @click="openDemo"
            >
              {{ $t('hero.cta.secondary') }}
            </button>
          </div>
        </div>
      </div>
      
      <!-- 滚动指示器 -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <Icon name="heroicons:chevron-down" class="w-6 h-6 text-slate-400" />
      </div>
    </section>

    <!-- 三大支柱 Section -->
    <section class="py-24 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl sm:text-5xl font-bold text-gradient mb-4">
            {{ $t('pillars.title') }}
          </h2>
          <p class="text-xl text-slate-400 max-w-3xl mx-auto">
            {{ $t('pillars.subtitle') }}
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- AI 智能创作 -->
          <div class="glass p-8 rounded-2xl card-hover">
            <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-6">
              <Icon name="heroicons:cpu-chip" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">
              {{ $t('pillars.ai.title') }}
            </h3>
            <p class="text-slate-400 leading-relaxed">
              {{ $t('pillars.ai.description') }}
            </p>
          </div>
          
          <!-- 沉浸式交互 -->
          <div class="glass p-8 rounded-2xl card-hover">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-6">
              <Icon name="heroicons:cube-transparent" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">
              {{ $t('pillars.interactive.title') }}
            </h3>
            <p class="text-slate-400 leading-relaxed">
              {{ $t('pillars.interactive.description') }}
            </p>
          </div>
          
          <!-- 共创生态 -->
          <div class="glass p-8 rounded-2xl card-hover">
            <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center mb-6">
              <Icon name="heroicons:users" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">
              {{ $t('pillars.collaborative.title') }}
            </h3>
            <p class="text-slate-400 leading-relaxed">
              {{ $t('pillars.collaborative.description') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 在线演示 Section -->
    <section class="py-24 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-4xl sm:text-5xl font-bold text-gradient mb-4">
            {{ $t('demo.title') }}
          </h2>
          <p class="text-xl text-slate-400">
            {{ $t('demo.subtitle') }}
          </p>
        </div>
        
        <DemoSection />
      </div>
    </section>

    <!-- 价格快照 Section -->
    <section class="py-24 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl sm:text-5xl font-bold text-gradient mb-4">
            {{ $t('pricing.title') }}
          </h2>
          <p class="text-xl text-slate-400 max-w-3xl mx-auto">
            {{ $t('pricing.subtitle') }}
          </p>
        </div>
        
        <PricingCards />
      </div>
    </section>

    <!-- 社会证明 Section -->
    <section class="py-24 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl sm:text-5xl font-bold text-gradient mb-4">
            {{ $t('testimonials.title') }}
          </h2>
          <p class="text-xl text-slate-400">
            {{ $t('testimonials.subtitle') }}
          </p>
        </div>
        
        <TestimonialCarousel />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()

// 角色轮播
const roles = ['creators', 'players', 'developers']
const currentRoleIndex = ref(0)
const currentRole = computed(() => t(`hero.roles.${roles[currentRoleIndex.value]}`))

// 角色轮播逻辑
let roleInterval: NodeJS.Timeout

onMounted(() => {
  roleInterval = setInterval(() => {
    currentRoleIndex.value = (currentRoleIndex.value + 1) % roles.length
  }, 3000)
})

onUnmounted(() => {
  if (roleInterval) {
    clearInterval(roleInterval)
  }
})

// 打开演示模态框
const openDemo = () => {
  // 这里可以打开演示模态框或跳转到演示页面
  console.log('Open demo modal')
}

// SEO 配置
useHead({
  title: 'NEXT Novel - 全球首个 AIGC 多模态沉浸式故事共创平台',
  meta: [
    { name: 'description', content: 'NEXT Novel 结合前沿 AI 技术，让每个人都能创造属于自己的互动故事世界。支持文本、图像、音频的智能生成，打造真正的沉浸式故事体验。' }
  ]
})
</script>

<style scoped>
/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float {
  animation: float 6s ease-in-out infinite;
}
</style>
