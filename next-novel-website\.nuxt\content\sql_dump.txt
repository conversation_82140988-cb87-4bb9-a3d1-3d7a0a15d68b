CREATE TABLE IF NOT EXISTS _content_info (id TEXT PRIMARY KEY, "ready" BO<PERSON>EAN, "structureVersion" VARCHAR, "version" VARCHAR, "__hash__" TEXT UNIQUE); -- structure
INSERT INTO _content_info VALUES ('checksum_content', false, 'bgIYhpjRuV8zbHJE_CfelwKpJ_Td6YuGJwixiek8lmI', 'v3.5.0--bgIYhpjRuV8zbHJE_CfelwKpJ_Td6YuGJwixiek8lmI', 'Y3W9btHTWafFT1Y4dZvICwj909Y4Qyfeiw6nXFbAykc'); -- meta
DROP TABLE IF EXISTS _content_content; -- structure
CREATE TABLE IF NOT EXISTS _content_content (id TEXT PRIMARY KEY, "title" VARCHAR, "body" TEXT, "description" VARCHAR, "extension" VARCHAR, "meta" TEXT, "navigation" TEXT DEFAULT true, "path" VARCHAR, "seo" TEXT DEFAULT '{}', "stem" VARCHAR, "__hash__" TEXT UNIQUE); -- structure
UPDATE _content_info SET ready = true WHERE id = 'checksum_content'; -- meta