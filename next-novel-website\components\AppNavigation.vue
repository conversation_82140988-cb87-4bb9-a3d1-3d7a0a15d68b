<template>
  <nav
    class="fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ease-out"
    :class="{
      '-translate-y-full': !appStore.isNavVisible,
      'translate-y-0': appStore.isNavVisible
    }"
  >
    <div class="glass border-b border-white/10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex-shrink-0">
            <NuxtLink to="/" class="flex items-center space-x-2 group">
              <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">N</span>
              </div>
              <span class="text-xl font-bold text-gradient group-hover:scale-105 transition-transform duration-200">
                NEXT Novel
              </span>
            </NuxtLink>
          </div>

          <!-- 桌面端导航菜单 -->
          <div class="hidden lg:block">
            <div class="ml-10 flex items-baseline space-x-8">
              <NuxtLink
                v-for="item in navigationItems"
                :key="item.name"
                :to="item.href"
                class="nav-link"
                :class="{ 'nav-link-active': $route.path === item.href }"
              >
                {{ $t(item.name) }}
              </NuxtLink>
            </div>
          </div>

          <!-- 右侧操作区 -->
          <div class="flex items-center space-x-4">
            <!-- 语言切换 -->
            <LanguageSwitcher class="hidden sm:block" />
            
            <!-- 用户配额显示 -->
            <div
              v-if="appStore.user.isAuthenticated"
              class="hidden sm:flex items-center space-x-2 px-3 py-1 rounded-full bg-slate-800/50 text-sm"
            >
              <div class="w-2 h-2 rounded-full bg-green-400"></div>
              <span>{{ appStore.user.quota.remaining }}/{{ appStore.user.quota.total }}</span>
            </div>

            <!-- 登录/注册按钮 -->
            <div v-if="!appStore.user.isAuthenticated" class="hidden sm:flex items-center space-x-2">
              <NuxtLink
                to="/login"
                class="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white transition-colors duration-200"
              >
                {{ $t('nav.login') }}
              </NuxtLink>
              <NuxtLink
                to="/signup"
                class="btn-magnetic px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:shadow-lg hover:shadow-indigo-500/25 transition-all duration-200"
              >
                {{ $t('nav.signup') }}
              </NuxtLink>
            </div>

            <!-- 用户头像菜单 -->
            <UserMenu v-else class="hidden sm:block" />

            <!-- 移动端菜单按钮 -->
            <button
              class="lg:hidden p-2 rounded-md text-slate-400 hover:text-white hover:bg-slate-800 focus-ring transition-colors duration-200"
              @click="appStore.toggleMobileMenu()"
            >
              <Icon
                :name="appStore.isMobileMenuOpen ? 'heroicons:x-mark' : 'heroicons:bars-3'"
                class="w-6 h-6"
              />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div
      v-if="appStore.isMobileMenuOpen"
      class="lg:hidden absolute top-full left-0 right-0 glass border-b border-white/10"
    >
      <div class="px-4 py-4 space-y-2">
        <NuxtLink
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.href"
          class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-800 transition-colors duration-200"
          :class="{ 'text-white bg-slate-800': $route.path === item.href }"
        >
          {{ $t(item.name) }}
        </NuxtLink>
        
        <!-- 移动端用户操作 -->
        <div class="pt-4 border-t border-slate-700">
          <div v-if="!appStore.user.isAuthenticated" class="space-y-2">
            <NuxtLink
              to="/login"
              class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-800 transition-colors duration-200"
            >
              {{ $t('nav.login') }}
            </NuxtLink>
            <NuxtLink
              to="/signup"
              class="block px-3 py-2 rounded-md text-base font-medium bg-gradient-to-r from-indigo-500 to-purple-600 text-white"
            >
              {{ $t('nav.signup') }}
            </NuxtLink>
          </div>
          
          <!-- 移动端语言切换 -->
          <div class="pt-2">
            <LanguageSwitcher />
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()
const { t } = useI18n()

// 导航菜单项
const navigationItems = [
  { name: 'nav.home', href: '/' },
  { name: 'nav.product', href: '/product' },
  { name: 'nav.creators', href: '/creators' },
  { name: 'nav.pricing', href: '/pricing' },
  { name: 'nav.docs', href: '/docs' },
  { name: 'nav.community', href: '/community' }
]
</script>

<style scoped>
.nav-link {
  @apply px-3 py-2 rounded-md text-sm font-medium text-slate-300 hover:text-white transition-all duration-200 relative;
}

.nav-link::after {
  content: '';
  @apply absolute bottom-0 left-1/2 w-0 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-600 transition-all duration-300;
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link-active::after {
  @apply w-full;
}

.nav-link-active {
  @apply text-white;
}
</style>
