import { defineStore } from 'pinia'

export interface AppState {
  // 全局加载状态
  isLoading: boolean
  
  // 导航状态
  isNavVisible: boolean
  isMobileMenuOpen: boolean
  
  // 主题和偏好设置
  colorMode: 'light' | 'dark' | 'system'
  
  // 动效设置
  prefersReducedMotion: boolean
  isLowFPS: boolean
  
  // 用户状态
  user: {
    isAuthenticated: boolean
    profile: any | null
    quota: {
      remaining: number
      total: number
    }
  }
  
  // 错误处理
  error: {
    message: string
    code?: string
  } | null
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    isLoading: false,
    isNavVisible: true,
    isMobileMenuOpen: false,
    colorMode: 'dark',
    prefersReducedMotion: false,
    isLowFPS: false,
    user: {
      isAuthenticated: false,
      profile: null,
      quota: {
        remaining: 0,
        total: 0
      }
    },
    error: null
  }),

  getters: {
    // 检查是否应该显示动效
    shouldAnimate: (state) => !state.prefersReducedMotion && !state.isLowFPS,
    
    // 获取用户配额百分比
    quotaPercentage: (state) => {
      if (state.user.quota.total === 0) return 0
      return (state.user.quota.remaining / state.user.quota.total) * 100
    },
    
    // 检查配额是否不足
    isQuotaLow: (state) => {
      return state.user.quota.remaining < state.user.quota.total * 0.1
    }
  },

  actions: {
    // 设置加载状态
    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    // 切换导航可见性
    toggleNavVisibility() {
      this.isNavVisible = !this.isNavVisible
    },

    // 设置导航可见性
    setNavVisibility(visible: boolean) {
      this.isNavVisible = visible
    },

    // 切换移动端菜单
    toggleMobileMenu() {
      this.isMobileMenuOpen = !this.isMobileMenuOpen
    },

    // 关闭移动端菜单
    closeMobileMenu() {
      this.isMobileMenuOpen = false
    },

    // 设置颜色模式
    setColorMode(mode: 'light' | 'dark' | 'system') {
      this.colorMode = mode
    },

    // 检测并设置动效偏好
    detectMotionPreference() {
      if (process.client) {
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
        this.prefersReducedMotion = mediaQuery.matches
        
        // 监听变化
        mediaQuery.addEventListener('change', (e) => {
          this.prefersReducedMotion = e.matches
        })
      }
    },

    // 性能监控 - 检测低帧率
    startFPSMonitoring() {
      if (process.client && !this.prefersReducedMotion) {
        let lastTime = performance.now()
        let frameCount = 0
        let lowFPSCount = 0
        
        const checkFPS = () => {
          const currentTime = performance.now()
          frameCount++
          
          if (currentTime - lastTime >= 1000) {
            const fps = frameCount
            frameCount = 0
            lastTime = currentTime
            
            if (fps < 30) {
              lowFPSCount++
              if (lowFPSCount >= 3) {
                this.isLowFPS = true
                console.warn('Low FPS detected, disabling animations')
                return
              }
            } else {
              lowFPSCount = 0
            }
          }
          
          if (!this.isLowFPS) {
            requestAnimationFrame(checkFPS)
          }
        }
        
        requestAnimationFrame(checkFPS)
      }
    },

    // 设置用户认证状态
    setAuthenticated(authenticated: boolean, profile?: any) {
      this.user.isAuthenticated = authenticated
      this.user.profile = profile || null
    },

    // 更新用户配额
    updateQuota(remaining: number, total: number) {
      this.user.quota.remaining = remaining
      this.user.quota.total = total
    },

    // 设置错误
    setError(message: string, code?: string) {
      this.error = { message, code }
    },

    // 清除错误
    clearError() {
      this.error = null
    },

    // 初始化应用
    async initialize() {
      this.detectMotionPreference()
      this.startFPSMonitoring()
      
      // 检查用户认证状态
      try {
        // 这里可以调用 API 检查用户状态
        // const user = await $fetch('/api/auth/me')
        // this.setAuthenticated(true, user)
      } catch (error) {
        // 用户未认证或其他错误
        this.setAuthenticated(false)
      }
    }
  },

  // persist: {
  //   storage: persistedState.localStorage,
  //   paths: ['colorMode', 'user.profile']
  // }
})
